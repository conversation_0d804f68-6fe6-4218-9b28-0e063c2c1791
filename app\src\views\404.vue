<template>
  <div class="not-found">
    <div class="content">
      <div class="icon">404</div>
      <div class="title">页面不存在</div>
      <div class="desc">您访问的页面不存在或已被删除</div>
      <div class="btn-back" @click="goHome">返回首页</div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'NotFound',
    methods: {
      goHome() {
        this.$router.replace('/')
      }
    }
  }
</script>

<style scoped>
  .not-found {
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    background-color: #f7f8fa;
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .icon {
    font-size: 80px;
    font-weight: bold;
    color: #1989fa;
    margin-bottom: 20px;
  }

  .title {
    font-size: 22px;
    font-weight: 500;
    margin-bottom: 10px;
  }

  .desc {
    font-size: 14px;
    color: #999;
    margin-bottom: 30px;
    text-align: center;
  }

  .btn-back {
    padding: 10px 25px;
    background-color: #1989fa;
    color: #fff;
    border-radius: 20px;
    font-size: 14px;
  }
</style>
