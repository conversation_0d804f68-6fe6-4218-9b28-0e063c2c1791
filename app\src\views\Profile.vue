<template>
  <page-layout :title="userInfo?.nickname || userInfo?.username || '用户资料'" :show-back="true" hide-tab-bar>
    <template #header-right>
      <div class="profile-options" @click="showOptions = true">
        <i class="icon-more">⋮</i>
      </div>
    </template>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="skeleton-container">
        <div class="skeleton-avatar" />
        <div class="skeleton-name" />
        <div class="skeleton-info" />
        <div class="skeleton-tags" />
        <div class="skeleton-photos" />
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="hasError" class="error-container">
      <div class="error-content">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="48"
          height="48"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <circle cx="12" cy="12" r="10"/>
          <line x1="12" y1="8" x2="12" y2="12"/>
          <line x1="12" y1="16" x2="12.01" y2="16"/>
        </svg>
        <h3>{{ errorMessage }}</h3>
        <button class="retry-button" @click="reloadData">重新加载</button>
      </div>
    </div>

    <!-- 用户资料内容 -->
    <div v-else class="profile-container">
      <!-- 基本信息部分 -->
      <div class="user-info-section">
        <div class="user-avatar-container" @click="previewPhoto(userInfo?.avatar || userInfo?.image)">
          <avatar :src="userInfo?.avatar || userInfo?.image" size="large" fill />
        </div>

        <div class="user-basic-info">
          <h1 class="user-name">
            {{ userInfo?.nickname || userInfo?.username }}
            <span class="user-age">{{ userInfo?.age }}岁</span>
          </h1>
          <div class="user-location">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
              <circle cx="12" cy="10" r="3" />
            </svg>
            <span>{{ userInfo?.region || '未设置地区' }}</span>
          </div>
        </div>
      </div>

      <!-- 标签部分 -->
      <div class="user-tags-section">
        <h2 class="section-title">兴趣标签</h2>
        <div class="tag-container">
          <div v-for="(tag, index) in userInfo?.tags" :key="index" class="tag-item">
            {{ tag }}
          </div>
        </div>
      </div>

      <!-- 相册部分 -->
      <div class="user-photos-section">
        <h2 class="section-title">相册</h2>
        <div class="photo-grid">
          <div
            v-for="(photo, index) in photos"
            :key="index"
            class="photo-item"
            @click="previewPhoto(photo.url)"
          >
            <img v-lazy="photo.url" :alt="`照片 ${index + 1}`" />
          </div>
        </div>
      </div>

      <!-- 个人简介部分 -->
      <div class="user-bio-section">
        <h2 class="section-title">个人简介</h2>
        <p class="bio-text">{{ userInfo?.bio || '这个人很懒，什么都没有留下...' }}</p>
      </div>

      <!-- 用户帖子部分 -->
      <div class="user-posts-section">
        <h2 class="section-title">帖子</h2>
        <div v-if="userPosts.length > 0" class="post-list">
          <post-card
            v-for="post in userPosts"
            :key="post.id"
            :post="post"
            @like="likePost"
            @comment="commentPost"
            @share="sharePost"
            @toggle-privacy="togglePostPrivacy"
            @delete="deletePost"
          />
        </div>
        <div v-else class="no-posts">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="40"
            height="40"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="1"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
            <polyline points="14 2 14 8 20 8" />
            <line x1="16" y1="13" x2="8" y2="13" />
            <line x1="16" y1="17" x2="8" y2="17" />
            <polyline points="10 9 9 9 8 9" />
          </svg>
          <p>暂无帖子</p>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-container">
      <button class="action-button dislike" @click="handleDislike">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <line x1="18" y1="6" x2="6" y2="18" />
          <line x1="6" y1="6" x2="18" y2="18" />
        </svg>
      </button>

      <button class="action-button message" @click="handleMessage">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
        </svg>
      </button>

      <button class="action-button like" @click="handleLike">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path
            d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
          />
        </svg>
      </button>
    </div>

    <!-- 照片预览 -->
    <transition name="fade">
      <div v-if="previewUrl" class="photo-preview" @click="closePreview">
        <div v-if="previewLoading" class="photo-preview-loading" />
        <img
          v-lazy="previewUrl"
          :class="{ loaded: !previewLoading }"
          @load="previewLoading = false"
        />
      </div>
    </transition>

    <!-- 选项菜单 -->
    <transition name="fade">
      <div v-if="showOptions" class="options-panel" @click="showOptions = false">
        <transition name="slide-up">
          <div v-if="showOptions" class="options-content" @click.stop>
            <div class="options-header">
              <span>用户选项</span>
              <span class="options-close" @click="showOptions = false">×</span>
            </div>

            <div class="options-group">
              <div class="option-item" @click="handleReport">
                <div class="option-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"
                    />
                    <line x1="12" y1="9" x2="12" y2="13" />
                    <line x1="12" y1="17" x2="12.01" y2="17" />
                  </svg>
                </div>
                <span>举报此人</span>
              </div>

              <div class="option-item" @click="handleBlock">
                <div class="option-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <line x1="4.93" y1="4.93" x2="19.07" y2="19.07" />
                  </svg>
                </div>
                <span>屏蔽此人</span>
              </div>
            </div>

            <div class="option-item cancel" @click="showOptions = false">取消</div>
          </div>
        </transition>
      </div>
    </transition>
  </page-layout>
</template>

<script setup>
  import { ref, onMounted, inject } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import PageLayout from '../components/PageLayout.vue'
  import Avatar from '../components/Avatar.vue'
  import PostCard from '../components/PostCard.vue'
  import { userService } from '../services/userService'
  import { postService } from '../services/postService'

  const route = useRoute()
  const router = useRouter()
  const toast = inject('toast')

  // 加载状态
  const isLoading = ref(true)
  const hasError = ref(false)
  const errorMessage = ref('')

  // 获取用户ID
  const userId = ref(Number(route.params.id))

  // 用户信息
  const userInfo = ref(null)

  // 相册照片
  const photos = ref([])

  // 照片预览
  const previewUrl = ref(null)
  const previewLoading = ref(true)

  // 选项菜单
  const showOptions = ref(false)

  // 用户帖子
  const userPosts = ref([])

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      isLoading.value = true
      const response = await userService.getUserProfile(userId.value)
      const userData = response
      
      // 设置用户信息
      userInfo.value = userData
      
      // 记录访问（静默处理）
      try {
        if (userData.id !== userStore.userInfo.id) {
          await userService.recordVisit(userData.id)
        }
      } catch (error) {
        // 静默处理记录访问失败
      }
    } catch (error) {
      toast.error('获取用户信息失败')
      router.back()
    } finally {
      isLoading.value = false
    }
  }

  // 预览照片
  const previewPhoto = url => {
    if (!url) return
    previewUrl.value = url
    previewLoading.value = true
  }

  // 关闭预览
  const closePreview = () => {
    previewUrl.value = null
  }

  // 处理喜欢按钮
  const handleLike = async () => {
    if (!canInteract.value) return
    
    try {
      // 发送喜欢请求
      const response = await matchService.likeUser(userId.value)
      
      if (response.matched) {
        toast.success('恭喜！你们匹配成功了 🎉')
      } else {
        toast.success('已表达喜欢')
      }
      
      // 返回上一页
      setTimeout(() => {
        router.back()
      }, 1500)
    } catch (error) {
      toast.error('操作失败')
    }
  }

  // 处理不喜欢按钮
  const handleDislike = async () => {
    if (!canInteract.value) return
    
    try {
      await matchService.skipUser(userId.value)
      router.back()
    } catch (error) {
      toast.error('操作失败')
    }
  }

  // 处理发消息按钮
  const handleMessage = async () => {
    try {
      // 检查是否已经匹配
      const isMatched = await userService.checkMatchStatus(userId.value)
      
      if (!isMatched) {
        toast.show('需要双方互相喜欢才能发消息', 'warning')
        return
      }
      
      router.push(`/chat/${userId.value}`)
    } catch (error) {
      // 静默处理检查匹配状态失败
      return false
    }
  }

  // 处理举报
  const handleReport = async () => {
    if (confirm('确定要举报此人吗？')) {
      try {
        await userService.reportUser(userId.value, { reason: '违规内容' })
        toast.success('举报已提交')
        showOptions.value = false
      } catch (error) {
        toast.error('举报失败')
      }
    }
  }

  // 处理屏蔽
  const handleBlock = async () => {
    if (confirm('确定要屏蔽此人吗？屏蔽后将不会再看到此用户。')) {
      try {
        await userService.blockUser(userId.value)
        toast.success('已屏蔽该用户')
        showOptions.value = false
        router.back()
      } catch (error) {
        toast.error('屏蔽失败')
      }
    }
  }

  // 获取用户帖子
  const fetchUserPosts = async () => {
    try {
      const response = await postService.getUserPosts(userId.value, {
        page: 1,
        pageSize: 20
      })
      
      const posts = response.data || response || []
      
      // 处理帖子数据
      userPosts.value = posts.map(post => ({
        id: post.id,
        content: post.content,
        images: post.images || [],
        likeCount: post.likeCount || 0,
        commentCount: post.commentCount || 0,
        shareCount: post.shareCount || 0,
        isLiked: post.isLiked || false,
        isPrivate: post.isPrivate || false,
        createdAt: post.createdAt,
        user: post.user || userInfo.value
      }))
    } catch (error) {
      // 静默处理获取用户帖子失败
      userPosts.value = []
    }
  }

  // 点赞帖子
  const likePost = async postId => {
    try {
      const post = userPosts.value.find(p => p.id === postId)
      if (!post) return

      const newLikeStatus = !post.isLiked
      await postService.toggleLike(postId)
      
      post.isLiked = newLikeStatus
      post.likeCount += newLikeStatus ? 1 : -1
      
      toast.show(newLikeStatus ? '已点赞' : '已取消点赞', 'success')
    } catch (error) {
      toast.error('点赞失败')
    }
  }

  // 评论帖子
  const commentPost = postId => {
    router.push(`/post/${postId}`)
  }

  // 分享帖子
  const sharePost = async postId => {
    try {
      await postService.sharePost(postId)
      
      // 更新分享数
      const post = userPosts.value.find(p => p.id === postId)
      if (post) {
        post.shareCount += 1
      }
      
      toast.success('分享成功')
    } catch (error) {
      toast.error('分享失败')
    }
  }

  // 切换帖子隐私状态
  const togglePostPrivacy = async postId => {
    try {
      const post = userPosts.value.find(p => p.id === postId)
      if (!post) return
      
      const newPrivacy = !post.isPrivate
      await postService.updatePostPrivacy(postId, newPrivacy)
      
      // 更新本地状态
      post.isPrivate = newPrivacy
      
      if (newPrivacy) {
        toast.success('帖子已设为私密')
      } else {
        toast.success('帖子已设为公开')
      }
    } catch (error) {
      toast.error('操作失败')
    }
  }

  // 删除帖子
  const deletePost = async postId => {
    if (!confirm('确定要删除这个帖子吗？删除后无法恢复。')) {
      return
    }

    try {
      await postService.deletePost(postId)
      const index = userPosts.value.findIndex(p => p.id === postId)
      if (index !== -1) {
        userPosts.value.splice(index, 1)
      }
      toast.success('帖子已删除')
    } catch (error) {
      toast.error('删除失败')
    }
  }

  // 重新加载数据
  const reloadData = () => {
    fetchUserInfo()
    fetchUserPosts()
  }

  // 组件挂载后执行
  onMounted(() => {
    if (!userId.value || isNaN(userId.value)) {
      hasError.value = true
      errorMessage.value = '无效的用户ID'
      return
    }

    fetchUserInfo()
    fetchUserPosts()
  })
</script>

<style scoped>
  .profile-options {
    font-size: 24px;
    color: #666;
    cursor: pointer;
    padding: 0 5px;
  }

  /* 加载状态和骨架屏 */
  .loading-container {
    flex: 1;
    overflow-y: auto;
    margin: 0 -15px;
    padding: 0 15px 10px;
    background-color: #ffffff;
  }

  .skeleton-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 20px 0;
    align-items: center;
  }

  .skeleton-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: #f0f0f0;
    animation: pulse 1.5s infinite;
  }

  .skeleton-name {
    width: 180px;
    height: 30px;
    background-color: #f0f0f0;
    border-radius: 6px;
    animation: pulse 1.5s infinite;
    margin-top: 10px;
  }

  .skeleton-info {
    width: 140px;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 6px;
    animation: pulse 1.5s infinite;
    margin-top: 5px;
  }

  .skeleton-tags {
    width: 90%;
    height: 40px;
    background-color: #f0f0f0;
    border-radius: 6px;
    animation: pulse 1.5s infinite;
    margin-top: 20px;
  }

  .skeleton-photos {
    width: 100%;
    height: 200px;
    background-color: #f0f0f0;
    border-radius: 12px;
    animation: pulse 1.5s infinite;
    margin-top: 20px;
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.6;
    }
  }

  /* 错误状态 */
  .error-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
  }

  .error-content {
    text-align: center;
    color: #666;
  }

  .error-content svg {
    color: #ff6b6b;
    margin-bottom: 20px;
  }

  .error-content h3 {
    font-size: 18px;
    font-weight: 500;
    margin: 0 0 20px 0;
    color: #333;
  }

  .retry-button {
    background-color: var(--color-primary);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .retry-button:hover {
    background-color: var(--color-primary-dark, #0056b3);
    transform: translateY(-2px);
  }

  .profile-container {
    flex: 1;
    overflow-y: auto;
    margin: 0 -15px;
    padding: 0 15px 80px; /* 底部留出空间给操作按钮 */
    background-color: #ffffff;
  }

  /* 用户基本信息区域 */
  .user-info-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    position: relative;
  }

  .user-avatar-container {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .user-basic-info {
    margin-top: 15px;
    text-align: center;
  }

  .user-name {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0;
  }

  .user-age {
    font-size: 18px;
    font-weight: normal;
    color: #666;
    margin-left: 5px;
  }

  .user-location {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 8px;
    color: #777;
    font-size: 14px;
  }

  .user-location svg {
    width: 16px;
    height: 16px;
    margin-right: 5px;
    color: var(--color-primary);
  }

  /* 标签部分 */
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-top: 25px;
    margin-bottom: 15px;
    position: relative;
    padding-left: 15px;
  }

  .section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 18px;
    background-color: var(--color-primary);
    border-radius: 2px;
  }

  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .tag-item {
    background-color: #f7f7f7;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 14px;
    color: #666;
  }

  /* 相册部分 */
  .photo-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }

  .photo-item {
    aspect-ratio: 1;
    overflow: hidden;
    border-radius: 8px;
    position: relative;
    cursor: pointer;
  }

  .photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .photo-item:hover img {
    transform: scale(1.05);
  }

  /* 个人简介部分 */
  .bio-text {
    font-size: 15px;
    color: #555;
    line-height: 1.6;
  }

  /* 操作按钮区域 */
  .action-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 15px 30px;
    background: white;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
  }

  .action-button {
    width: 54px;
    height: 54px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition:
      transform 0.2s,
      box-shadow 0.2s;
  }

  .action-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .action-button.dislike {
    background-color: white;
    color: var(--color-primary);
    border: 1px solid #ffebeb;
  }

  .action-button.like {
    background-color: var(--color-primary);
    color: white;
  }

  .action-button.message {
    background-color: white;
    color: #4a90e2;
    border: 1px solid #e6f0ff;
  }

  /* 照片预览 */
  .photo-preview {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .photo-preview img {
    max-width: 90%;
    max-height: 90%;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .photo-preview img.loaded {
    opacity: 1;
  }

  .photo-preview-loading {
    width: 48px;
    height: 48px;
    position: absolute;
    border: 3px solid transparent;
    border-top-color: white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /* 选项面板 */
  .options-panel {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: flex-end;
    justify-content: center;
    z-index: 1000;
  }

  .options-content {
    width: 100%;
    background-color: #fff;
    border-radius: 20px 20px 0 0;
    overflow: hidden;
    max-width: 600px;
    transform: translateY(0);
  }

  .options-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
  }

  .options-header span {
    font-size: 16px;
    font-weight: 500;
  }

  .options-close {
    font-size: 22px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    cursor: pointer;
  }

  .options-group {
    padding: 5px 0;
    border-bottom: 1px solid #f5f5f5;
  }

  .option-item {
    padding: 15px;
    display: flex;
    align-items: center;
    transition: background-color 0.2s;
    cursor: pointer;
  }

  .option-item:active {
    background-color: #f5f5f5;
  }

  .option-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: #666;
  }

  .option-item span {
    font-size: 16px;
  }

  .option-item.cancel {
    text-align: center;
    color: var(--color-primary);
    font-weight: 500;
    justify-content: center;
    padding: 18px;
    margin: 8px 0;
  }

  /* 过渡效果 */
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }

  .slide-up-enter-active,
  .slide-up-leave-active {
    transition: transform 0.3s ease;
  }

  .slide-up-enter-from,
  .slide-up-leave-to {
    transform: translateY(100%);
  }

  /* 媒体查询，适应不同屏幕尺寸 */
  @media (max-width: 375px) {
    .user-avatar-container {
      width: 90px;
      height: 90px;
    }

    .user-name {
      font-size: 22px;
    }

    .action-button {
      width: 50px;
      height: 50px;
    }
  }

  @media (min-width: 768px) {
    .photo-grid {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  /* 帖子列表 */
  .user-posts-section {
    margin-top: 20px;
  }

  .post-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .no-posts {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    color: #999;
  }

  .no-posts svg {
    margin-bottom: 15px;
    stroke: #ccc;
  }

  .no-posts p {
    font-size: 16px;
  }
</style>
