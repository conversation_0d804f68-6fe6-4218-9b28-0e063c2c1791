<template>
  <div class="menu-item" @click="$emit('click')">
    <div class="menu-left">
      <div v-if="icon" class="menu-icon" :class="[icon, iconColor]" />
      <div class="menu-title">{{ title }}</div>
    </div>

    <div class="menu-right">
      <div v-if="badge" class="menu-badge" :class="{ 'menu-badge-dot': badge === 'dot' }">
        {{ badge !== 'dot' ? badge : '' }}
      </div>
      <i class="menu-arrow">
        <svg width="6" height="12" viewBox="0 0 6 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M0.5 1L5.5 6L0.5 11" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </i>
    </div>
  </div>
</template>

<script setup>
defineProps({
  icon: {
    type: String,
    default: ''
  },
  iconColor: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    required: true
  },
  badge: {
    type: [String, Number],
    default: ''
  }
})

defineEmits(['click'])
</script>

<style scoped>
.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  position: relative;
  cursor: pointer;
  min-height: 54px;
  box-sizing: border-box;
  width: 100%;
  transition: background-color var(--transition-fast);
}

.menu-item:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.menu-item:after {
  content: '';
  position: absolute;
  left: var(--spacing-md);
  right: var(--spacing-md);
  bottom: 0;
  height: 1px;
  background-color: var(--color-background);
}

.menu-item:last-child:after {
  display: none;
}

.menu-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.menu-icon {
  width: 24px;
  height: 24px;
  margin-right: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
  flex-shrink: 0;
  font-size: var(--font-size-xl);
}

.menu-icon.green {
  color: var(--color-success);
}

.menu-icon.blue {
  color: var(--color-info);
}

.menu-icon.purple {
  color: #9b59b6;
}

.menu-icon.orange {
  color: var(--color-warning);
}

.menu-title {
  flex: 1;
  font-size: var(--font-size-lg);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menu-right {
  display: flex;
  align-items: center;
}

.menu-badge {
  min-width: 16px;
  height: 16px;
  line-height: 16px;
  padding: 0 var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: #fff;
  background-color: var(--color-primary);
  border-radius: 8px;
  text-align: center;
  margin-right: var(--spacing-sm);
  flex-shrink: 0;
}

.menu-badge-dot {
  min-width: var(--spacing-sm);
  height: var(--spacing-sm);
  padding: 0;
  border-radius: calc(var(--spacing-sm) / 2);
}

.menu-arrow {
  display: flex;
  align-items: center;
  color: var(--font-color-light);
  flex-shrink: 0;
}

.menu-arrow svg path {
  stroke: var(--font-color-light);
}
</style>
