<template>
  <div class="auth-container">
    <div class="background-shapes">
      <div class="shape shape-1" />
      <div class="shape shape-2" />
      <div class="shape shape-3" />
    </div>

    <div class="auth-card">
      <div class="auth-header">
        <div class="logo">
          <svg
            width="60"
            height="60"
            viewBox="0 0 24 24"
            fill="none"
            stroke="#ff5864"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path
              d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
            />
          </svg>
        </div>
        <h2>用户登录</h2>
        <p>登录账号，开始使用全部功能</p>
      </div>

      <div class="form">
        <div class="form-item">
          <div class="input-wrapper">
            <svg
              class="input-icon"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
              <circle cx="12" cy="7" r="4" />
            </svg>
            <input
id="username"
v-model="form.username" type="text"
placeholder="请输入用户名" />
          </div>
        </div>

        <div class="form-item">
          <div class="input-wrapper">
            <svg
              class="input-icon"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <rect x="3" y="11" width="18" height="11" rx="2"
ry="2" />
              <path d="M7 11V7a5 5 0 0 1 10 0v4" />
            </svg>
            <input
id="password"
v-model="form.password" type="password"
placeholder="请输入密码" />
          </div>
        </div>

        <div class="form-actions">
          <button class="btn btn-primary" @click="handleLogin">
            <span class="btn-text">登 录</span>
            <span class="btn-icon">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path d="M5 12h14" />
                <path d="m12 5 7 7-7 7" />
              </svg>
            </span>
          </button>
        </div>

        <div class="form-footer">
          <p>
            还没有账号？
            <router-link to="/register">立即注册</router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, inject } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { useUserStore } from '../stores/userStore'
  import { authService } from '../services/authService'

  const router = useRouter()
  const route = useRoute()
  const userStore = useUserStore()
  const toast = inject('toast')

  const form = ref({
    username: '',
    password: ''
  })

  const handleLogin = async () => {
    try {
      if (!form.value.username || !form.value.password) {
        toast.warning('请填写完整的登录信息')
        return
      }

      // 调用登录API
      const response = await authService.login({
        username: form.value.username,
        password: form.value.password
      })

      // 保存token和用户信息
      userStore.setToken(response.token)
      userStore.setUserInfo(response.userInfo)

      toast.success('登录成功')

      // 延迟跳转，让用户看到提示
      setTimeout(() => {
        // 如果有重定向参数，则跳转到对应页面，否则跳转到首页
        const redirectPath = route.query.redirect || '/'
        router.push(redirectPath)
      }, 800)
    } catch (error) {
      toast.error(error.message || '登录失败，请稍后再试')
    }
  }
</script>

<style scoped>
  .form-footer {
    margin-top: 2rem;
    text-align: center;
    color: var(--font-color-secondary);
    font-size: 0.95rem;
  }

  .form-footer a {
    color: var(--color-primary);
    font-weight: 600;
  }
</style>
