/**
 * Socket 服务 - WebSocket 真实连接实现
 */

// 事件监听器
const listeners = {
  message: [],
  connect: [],
  disconnect: [],
  error: [],
  reconnect: []
}

// 连接状态和配置
let socket = null
let isConnected = false
let reconnectAttempts = 0
let maxReconnectAttempts = 5
let reconnectInterval = 3000
let reconnectTimer = null
let serverUrl = ''
let authToken = ''
let debugMode = false

// WebSocket 服务
const socketService = {
  // 配置服务器地址
  configure(config = {}) {
    serverUrl = config.url || import.meta.env.VITE_WS_URL || 'ws://localhost:3000/ws'
    maxReconnectAttempts = config.maxReconnectAttempts || 5
    reconnectInterval = config.reconnectInterval || 3000
    debugMode = config.debug || false
    return this
  },

  // 连接到服务器
  connect(token) {
    try {
      if (socket && socket.readyState === WebSocket.OPEN) {
        return
      }

      authToken = token
      isConnecting = true

      const wsUrl = this.buildWsUrl(token)
      
      socket = new WebSocket(wsUrl)

      socket.onopen = (event) => {
        try {
          isConnected = true
          isConnecting = false
          reconnectAttempts = 0
          
          // 清除重连定时器
          if (reconnectTimer) {
            clearTimeout(reconnectTimer)
            reconnectTimer = null
          }

          // 触发连接成功事件
          listeners.connect.forEach(handler => {
            try {
              handler(event)
            } catch (error) {
              // 静默处理连接事件回调错误
            }
          })
        } catch (error) {
          // 静默处理连接事件回调错误
        }
      }

      socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          
          // 触发消息事件
          listeners.message.forEach(handler => {
            try {
              handler(data)
            } catch (error) {
              // 静默处理消息事件回调错误
            }
          })
        } catch (error) {
          // 静默处理消息解析错误
        }
      }

      socket.onclose = (event) => {
        try {
          isConnected = false
          isConnecting = false

          // 触发断开连接事件
          listeners.disconnect.forEach(handler => {
            try {
              handler(event)
            } catch (error) {
              // 静默处理断开连接事件回调错误
            }
          })

          // 如果不是手动关闭，尝试重连
          if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
            this.scheduleReconnect()
          }
        } catch (error) {
          // 静默处理断开连接事件回调错误
        }
      }

      socket.onerror = (error) => {
        try {
          isConnected = false

          // 触发错误事件
          listeners.error.forEach(handler => {
            try {
              handler(error)
            } catch (callbackError) {
              // 静默处理错误事件回调错误
            }
          })
        } catch (error) {
          // 静默处理错误事件回调错误
        }
      }

    } catch (error) {
      try {
        // 触发错误事件
        listeners.error.forEach(handler => {
          try {
            handler(error)
          } catch (callbackError) {
            // 静默处理错误事件回调错误
          }
        })
      } catch (error) {
        // 静默处理错误事件回调错误
      }
    }

    return this
  },

  // 断开连接
  disconnect() {
    // 清除重连定时器
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }

    // 重置重连次数
    reconnectAttempts = maxReconnectAttempts

    if (socket) {
      // 手动关闭，使用 1000 状态码
      socket.close(1000, '手动断开连接')
      socket = null
    }

    isConnected = false
    return this
  },

  // 发送消息
  sendMessage(data) {
    if (!isConnected || !socket || socket.readyState !== WebSocket.OPEN) {
      return false
    }

    try {
      const message = typeof data === 'string' ? data : JSON.stringify(data)
      socket.send(message)
      return true
    } catch (error) {
      // 静默处理发送消息失败
      return false
    }
  },

  // 添加事件监听器
  on(event, callback) {
    if (!listeners[event]) {
      listeners[event] = []
    }

    listeners[event].push(callback)
    return this
  },

  // 移除事件监听器
  off(event, callback) {
    if (listeners[event]) {
      listeners[event] = listeners[event].filter(cb => cb !== callback)
    }
    return this
  },

  // 移除所有事件监听器
  removeAllListeners(event) {
    if (event && listeners[event]) {
      listeners[event] = []
    } else if (!event) {
      Object.keys(listeners).forEach(key => {
        listeners[key] = []
      })
    }
    return this
  },

  // 获取连接状态
  isConnected() {
    return isConnected && socket && socket.readyState === WebSocket.OPEN
  },

  // 获取 WebSocket 状态
  getReadyState() {
    if (!socket) return WebSocket.CLOSED
    return socket.readyState
  },

  // 获取重连状态
  getReconnectInfo() {
    return {
      attempts: reconnectAttempts,
      maxAttempts: maxReconnectAttempts,
      isReconnecting: !!reconnectTimer
    }
  },

  // 安排重连
  scheduleReconnect() {
    if (reconnectAttempts >= maxReconnectAttempts) {
      return
    }

    const delay = Math.min(reconnectInterval * Math.pow(2, reconnectAttempts), 30000)
    
    reconnectTimer = setTimeout(() => {
      reconnectAttempts++
      this.connect(authToken)
      
      // 触发重连事件
      listeners.reconnect.forEach(handler => {
        try {
          handler({ attempt: reconnectAttempts })
        } catch (error) {
          // 静默处理重连事件回调错误
        }
      })
    }, delay)
  },

  // 模拟接收消息（用于测试）
  simulateIncomingMessage(message) {
    if (!this.isConnected()) {
      return
    }

    // 触发消息事件
    listeners.message.forEach(handler => {
      try {
        handler(message)
      } catch (error) {
        // 静默处理模拟消息事件回调错误
      }
    })
  },

  // 构建 WebSocket URL
  buildWsUrl(token) {
    return token ? `${serverUrl}?token=${token}` : serverUrl
  }
}

export default socketService
