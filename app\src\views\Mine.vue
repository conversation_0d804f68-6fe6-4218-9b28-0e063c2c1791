<template>
  <page-layout title="我的">
    <template #header-right>
      <div class="settings-icon" @click="handleSettingsClick">
        <svg
          width="22"
          height="22"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <circle cx="12" cy="12" r="3" />
          <path
            d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"
          />
        </svg>
      </div>
    </template>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="skeleton-container">
        <div class="skeleton-avatar" />
        <div class="skeleton-name" />
        <div class="skeleton-stats" />
        <div class="skeleton-photos" />
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="hasError" class="error-container">
      <div class="error-content">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="48"
          height="48"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <circle cx="12" cy="12" r="10"/>
          <line x1="12" y1="8" x2="12" y2="12"/>
          <line x1="12" y1="16" x2="12.01" y2="16"/>
        </svg>
        <h3>加载失败</h3>
        <button class="retry-button" @click="reloadData">重新加载</button>
      </div>
    </div>

    <div v-else class="mine-content">
      <div class="user-profile">
        <avatar
          :src="userInfo.avatar || '/default.png'"
          size="xlarge"
          border
        />
        <div class="user-name">{{ userInfo.nickname || userInfo.username || '用户名' }}</div>
        <div class="user-id">ID: {{ userInfo.id || '123456789' }}</div>
      </div>

      <vip-card @click="handleVipClick" />

      <stats-card :items="statsItems" @click="handleStatClick" />

      <div class="photos-section">
        <div class="section-header">
          <h2>我的照片</h2>
          <div class="add-photo" @click="handleAddPhoto">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="12" y1="5" x2="12" y2="19" />
              <line x1="5" y1="12" x2="19" y2="12" />
            </svg>
            添加
          </div>
        </div>

        <div class="photos-grid">
          <div v-for="(photo, index) in photos" :key="photo.id || index" class="photo-item">
            <img v-lazy="photo.url" :alt="`照片 ${index + 1}`" />
            <div class="photo-overlay">
              <div class="photo-actions">
                <button 
                  v-if="photo.url !== userInfo.avatar"
                  class="photo-action set-avatar" 
                  title="设为头像" 
                  @click.stop="setAsAvatar(photo, index)" 
                  :disabled="uploading"
                >
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                    <circle cx="12" cy="7" r="4"/>
                  </svg>
                </button>
                <button class="photo-action delete" title="删除照片" @click.stop="deletePhoto(photo, index)" :disabled="uploading">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="3,6 5,6 21,6"/>
                    <path d="M19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                    <line x1="10" y1="11" x2="10" y2="17"/>
                    <line x1="14" y1="11" x2="14" y2="17"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <div v-if="photos.length < 6" class="photo-item add-photo-item" @click="handleAddPhoto">
            <div class="add-photo-content">
              <div v-if="uploading" class="uploading-indicator">
                <div class="spinner" />
                <span>上传中...</span>
              </div>
              <div v-else>
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <line x1="12" y1="5" x2="12" y2="19" />
                  <line x1="5" y1="12" x2="19" y2="12" />
                </svg>
                <span>添加照片</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="recent-visitors">
        <div class="section-header">
          <h2>最近访客</h2>
          <div class="view-more" @click="handleViewAllVisitors">
            查看全部
          </div>
        </div>

        <div class="visitors-list">
          <div v-if="loadingVisitors" class="loading-visitors">
            <div class="spinner" />
            <p>加载中...</p>
          </div>
          
          <div v-else-if="recentVisitors.length === 0" class="empty-visitors">
            <svg
              width="48"
              height="48"
              viewBox="0 0 24 24"
              fill="none"
              stroke="#ccc"
              stroke-width="1.5"
            >
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
              <circle cx="12" cy="7" r="4" />
            </svg>
            <p>暂无访客记录</p>
          </div>

          <div v-else class="visitors-grid">
            <div 
              v-for="visitor in recentVisitors" 
              :key="visitor.id" 
              class="visitor-item"
              @click="viewVisitorProfile(visitor.id)"
            >
              <avatar
                :src="visitor.avatar"
                size="medium"
              />
              <div class="visitor-info">
                <div class="visitor-name">{{ visitor.nickname || visitor.name || visitor.username }}</div>
                <div class="visitor-details">
                  <span class="visit-count">访问{{ visitor.visitCount }}次</span>
                  <span class="visit-time">{{ formatVisitTime(visitor.visitTime) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加底部空间，确保内容可以完全滚动 -->
      <div class="bottom-space" />
    </div>

    <!-- 隐藏的文件输入框 -->
    <input 
      ref="fileInput" 
      type="file" 
      accept="image/*" 
      multiple 
      style="display: none" 
      @change="handleFileChange" 
    />
  </page-layout>
</template>

<script setup>
  import { ref, onMounted, inject, computed, nextTick } from 'vue'
  import { useUserStore } from '../stores/userStore'
  import { useRouter } from 'vue-router'
  import PageLayout from '../components/PageLayout.vue'
  import Avatar from '../components/Avatar.vue'
  import VipCard from '../components/VipCard.vue'
  import StatsCard from '../components/StatsCard.vue'
  import { userService } from '../services/userService'
  import { uploadService } from '../services/uploadService'

  const toast = inject('toast')
  const userStore = useUserStore()
  const userInfo = computed(() => userStore.userInfo)
  const router = useRouter()

  // 状态管理
  const isLoading = ref(true)
  const hasError = ref(false)
  const uploading = ref(false)
  const loadingVisitors = ref(false)

  // 文件输入框引用
  const fileInput = ref(null)

  // 用户照片
  const photos = ref([])

  // 最近访客数据
  const recentVisitors = ref([])

  // 统计数据
  const statsItems = ref([
    { value: 0, label: '访客' },
    { value: 0, label: '喜欢' },
    { value: 0, label: '匹配' }
  ])

  // 获取用户数据
  const fetchUserData = async () => {
    try {
      isLoading.value = true
      hasError.value = false
      
      // 获取用户资料
      const userProfile = await userService.getProfile()
      if (userProfile) {
        userStore.setUserInfo(userProfile)
      }

      // 获取用户照片
      const userPhotos = await userService.getUserPhotos(userInfo.value.id)
      photos.value = userPhotos || []

      // 获取真实统计数据
      const stats = await userService.getUserStats()
      if (stats) {
        statsItems.value = [
          { value: stats.totalVisits || 0, label: '访问' },
          { value: stats.likes || 0, label: '喜欢' },
          { value: stats.matches || 0, label: '匹配' }
        ]
      }

    } catch (error) {
      toast.error('获取用户数据失败')
      hasError.value = true
    } finally {
      isLoading.value = false
    }
  }

  // 获取访客数据
  const fetchVisitorsData = async () => {
    try {
      loadingVisitors.value = true
      
      // 获取真实访客数据
      const visitorsData = await userService.getVisitors({ limit: 4 })
      
      if (visitorsData && visitorsData.data) {
        recentVisitors.value = visitorsData.data.map(visitor => ({
          ...visitor,
          visitTime: new Date(visitor.visitTime)
        }))
      } else {
        recentVisitors.value = []
      }

    } catch (error) {
      toast.error('获取访客数据失败')
      recentVisitors.value = []
    } finally {
      loadingVisitors.value = false
    }
  }

  // 重新加载数据
  const reloadData = () => {
    fetchUserData()
    fetchVisitorsData()
  }

  // 处理查看全部访客
  const handleViewAllVisitors = () => {
    router.push('/visitors')
  }

  // 查看访客资料
  const viewVisitorProfile = (visitorId) => {
    router.push(`/profile/${visitorId}`)
  }

  // 格式化访问时间
  const formatVisitTime = (visitTime) => {
    const now = new Date()
    const diff = now - visitTime
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 60) {
      return `${minutes}分钟前`
    } else if (hours < 24) {
      return `${hours}小时前`
    } else {
      return `${days}天前`
    }
  }

  // 处理统计点击
  const handleStatClick = (item) => {
    switch (item.label) {
      case '访问':
        router.push('/visitors')
        break
      case '喜欢':
        router.push('/likes')
        break
      case '匹配':
        router.push('/matches')
        break
      default:
        toast.info(`查看${item.label}功能开发中`)
    }
  }

  // 处理VIP点击
  const handleVipClick = () => {
    router.push('/vip')
  }

  // 处理设置点击
  const handleSettingsClick = () => {
    router.push('/settings')
  }

  // 添加照片
  const handleAddPhoto = () => {
    if (uploading.value) {
      return
    }
    
    if (photos.value.length >= 6) {
      toast.show('最多只能添加6张照片', 'warning')
      return
    }

    fileInput.value?.click()
  }

  // 处理文件选择
  const handleFileChange = async (event) => {
    const files = Array.from(event.target.files)
    
    if (files.length === 0) return

    // 检查文件数量限制
    if (photos.value.length + files.length > 6) {
      toast.show('最多只能添加6张照片', 'warning')
      return
    }

    try {
      uploading.value = true
      
      // 验证文件
      for (const file of files) {
        try {
          uploadService.validateFile(file, {
            allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
            maxSize: 10 * 1024 * 1024 // 10MB
          })
        } catch (error) {
          toast.show(error.message, 'error')
          return
        }
      }

      // 上传文件
      const uploadResults = await userService.uploadPhotos(files)
      
      if (uploadResults && uploadResults.urls) {
        // 添加到照片列表
        const newPhotos = uploadResults.urls.map((url, index) => ({
          id: Date.now() + index,
          url: url
        }))
        photos.value.push(...newPhotos)
        
        toast.show('照片上传成功！', 'success')
      }

    } catch (error) {
      toast.error('上传照片失败')
    } finally {
      uploading.value = false
      // 清空文件输入框
      if (fileInput.value) {
        fileInput.value.value = ''
      }
    }
  }

  // 设置为头像
  const setAsAvatar = async (photo, index) => {
    if (uploading.value) return
    
    if (confirm('确定要将此照片设置为头像吗？')) {
      try {
        uploading.value = true
        
        await userService.setAvatar(photo.url)
        
        // 更新本地用户信息
        const updatedUserInfo = { ...userInfo.value, avatar: photo.url }
        userStore.setUserInfo(updatedUserInfo)
        
        // 强制触发响应式更新
        await nextTick()
        
        toast.show('头像设置成功！', 'success')
      } catch (error) {
        toast.error('设置头像失败')
      } finally {
        uploading.value = false
      }
    }
  }

  // 删除照片
  const deletePhoto = async (photo, index) => {
    if (uploading.value) return
    
    if (confirm(`确定要删除第${index + 1}张照片吗？`)) {
      try {
        uploading.value = true
        
        // 调用删除照片的API
        // 使用照片在数组中的索引+1作为 photoId（因为后端期望从1开始的索引）
        await userService.deletePhoto(photo.id || (index + 1))
        
        photos.value.splice(index, 1)
        toast.show('照片删除成功！', 'success')
      } catch (error) {
        toast.error('删除照片失败')
      } finally {
        uploading.value = false
      }
    }
  }

  // 组件挂载
  onMounted(() => {
    fetchUserData()
    fetchVisitorsData()
  })
</script>

<style scoped>
  /* 整体内容容器 */
  .mine-content {
    width: 100%;
    padding-top: 10px;
    padding-bottom: 20px;
  }

  /* Mine页面特定样式 */
  .user-profile {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    background-color: #fff;
    border-radius: 12px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .user-name {
    font-size: 18px;
    font-weight: 600;
    margin-top: 10px;
    margin-bottom: 5px;
  }

  .user-id {
    font-size: 14px;
    color: #999;
    margin-bottom: 5px;
  }

  /* 照片区域 */
  .photos-section {
    background-color: #fff;
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .section-header h2 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
  }

  .add-photo {
    font-size: 14px;
    color: #ff5864;
    cursor: pointer;
    display: flex;
    align-items: center;
  }

  .add-photo svg {
    margin-right: 4px;
    stroke: #ff5864;
  }

  .photos-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }

  .photo-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
  }

  .photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .photo-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
    opacity: 1;
    transition: background 0.3s ease;
    padding: 12px 8px 8px;
  }

  .photo-item:hover .photo-overlay {
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  }

  .photo-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
  }

  .photo-action {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    color: white;
    transition: all 0.2s ease;
  }

  .photo-action svg {
    flex-shrink: 0;
  }

  .photo-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .photo-action:active {
    transform: translateY(0);
  }

  .photo-action.set-avatar {
    background-color: rgba(76, 175, 80, 0.9);
  }

  .photo-action.set-avatar:hover {
    background-color: rgba(76, 175, 80, 1);
  }

  .photo-action.delete {
    background-color: rgba(244, 67, 54, 0.9);
  }

  .photo-action.delete:hover {
    background-color: rgba(244, 67, 54, 1);
  }

  .add-photo-item {
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    min-height: 80px;
  }

  .add-photo-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #999;
  }

  .add-photo-content svg {
    margin-bottom: 5px;
    stroke: #999;
  }

  .menu-list {
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    width: 100%;
    margin-bottom: 15px;
  }

  /* 底部空间，确保内容可以完全滚动 */
  .bottom-space {
    height: 20px;
  }

  /* 设置图标样式 */
  .settings-icon {
    font-size: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
  }

  .settings-icon svg {
    stroke: #ff5864;
    transition:
      transform 0.3s ease,
      stroke 0.3s ease;
  }

  .settings-icon:hover {
    background-color: rgba(255, 88, 100, 0.1);
  }

  .settings-icon:hover svg {
    transform: rotate(30deg);
  }

  /* 媒体查询 */
  @media (max-width: 375px) {
    .user-profile {
      padding: 15px 0;
    }

    .photos-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  /* 最近访客区域 */
  .recent-visitors {
    background-color: #fff;
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .view-more {
    font-size: 14px;
    color: #ff5864;
    cursor: pointer;
  }

  .empty-visitors {
    text-align: center;
    padding: 30px 20px;
    color: #999;
  }

  .empty-visitors p {
    margin-top: 10px;
    font-size: 14px;
  }

  .visitors-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .visitor-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .visitor-item:hover {
    background-color: #f8f9fa;
  }

  .visitor-info {
    margin-left: 12px;
    flex: 1;
  }

  .visitor-name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
  }

  .visitor-details {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .visit-count {
    font-size: 12px;
    color: #ff5864;
    font-weight: 500;
  }

  .visit-time {
    font-size: 12px;
    color: #999;
  }

  /* 加载状态样式 */
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }

  .skeleton-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 300px;
    padding: 20px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .skeleton-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: #f0f0f0;
    margin-bottom: 15px;
    animation: pulse 1.5s infinite;
  }

  .skeleton-name {
    width: 150px;
    height: 20px;
    background-color: #f0f0f0;
    margin-bottom: 10px;
    border-radius: 4px;
    animation: pulse 1.5s infinite;
  }

  .skeleton-stats {
    width: 200px;
    height: 60px;
    background-color: #f0f0f0;
    margin-bottom: 20px;
    border-radius: 8px;
    animation: pulse 1.5s infinite;
  }

  .skeleton-photos {
    width: 100%;
    height: 200px;
    background-color: #f0f0f0;
    margin-bottom: 10px;
    border-radius: 8px;
    animation: pulse 1.5s infinite;
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.6;
    }
  }

  /* 错误状态样式 */
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }

  .error-content {
    text-align: center;
    padding: 20px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .error-content svg {
    width: 48px;
    height: 48px;
    margin-bottom: 10px;
  }

  .error-content h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
  }

  .retry-button {
    padding: 10px 20px;
    background-color: #ff5864;
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .retry-button:hover {
    background-color: #e54d5a;
  }

  /* 加载访客状态样式 */
  .loading-visitors {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
  }

  .spinner {
    width: 24px;
    height: 24px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-top-color: #ff5864;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /* 上传中指示器样式 */
  .uploading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .uploading-indicator span {
    font-size: 14px;
    color: #999;
  }

  /* 禁用状态 */
  .photo-action:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
</style>
