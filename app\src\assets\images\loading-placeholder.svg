<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
  <defs>
    <linearGradient id="loadingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#64b5f6" />
      <stop offset="100%" stop-color="#2196f3" />
    </linearGradient>
  </defs>
  
  <rect width="200" height="200" fill="#f5f5f5" />
  
  <!-- 主旋转环 -->
  <g transform="translate(100, 100)">
    <circle r="50" fill="none" stroke="url(#loadingGradient)" stroke-width="8" stroke-linecap="round" stroke-dasharray="1, 50" stroke-dashoffset="0">
      <animateTransform 
        attributeName="transform" 
        attributeType="XML" 
        type="rotate" 
        from="0" 
        to="360" 
        dur="2s" 
        repeatCount="indefinite" />
    </circle>
    
    <!-- 内部旋转环 -->
    <circle r="30" fill="none" stroke="url(#loadingGradient)" stroke-width="6" stroke-linecap="round" stroke-dasharray="1, 30" stroke-dashoffset="0" opacity="0.7">
      <animateTransform 
        attributeName="transform" 
        attributeType="XML" 
        type="rotate" 
        from="360" 
        to="0" 
        dur="1.5s" 
        repeatCount="indefinite" />
    </circle>
    
    <!-- 中心脉动圆点 -->
    <circle r="12" fill="url(#loadingGradient)">
      <animate 
        attributeName="r" 
        values="12;14;12" 
        dur="1.5s" 
        repeatCount="indefinite" />
      <animate 
        attributeName="opacity" 
        values="0.8;1;0.8" 
        dur="1.5s" 
        repeatCount="indefinite" />
    </circle>
  </g>
</svg> 