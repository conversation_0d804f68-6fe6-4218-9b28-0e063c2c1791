// 这里我们使用浏览器原生的fetch API
// 实际项目中可以替换为axios等库

import { API_BASE_URL } from '../config'

// 基础配置
const BASE_URL = API_BASE_URL
const TIME_OUT = 60000 // 超时时间，单位毫秒

/**
 * 获取认证token
 * @returns {string|null}
 */
const getAuthToken = () => {
  return localStorage.getItem('token')
}

/**
 * 统一的服务错误处理
 * @param {Error} error - 原始错误
 * @param {string} defaultMessage - 默认错误信息
 * @returns {Error} - 处理后的错误
 */
export const handleServiceError = (error, defaultMessage) => {
  return new Error(error.message || defaultMessage)
}

/**
 * 服务方法包装器，统一处理try-catch和错误处理
 * @param {Function} serviceMethod - 服务方法
 * @param {string} errorMessage - 错误信息
 * @returns {Function} - 包装后的方法
 */
export const withErrorHandler = (serviceMethod, errorMessage) => {
  return async (...args) => {
    try {
      return await serviceMethod(...args)
    } catch (error) {
      throw handleServiceError(error, errorMessage)
    }
  }
}

/**
 * 基础请求函数
 * @param {string} url 请求地址
 * @param {Object} options 请求配置
 * @returns {Promise}
 */
export const request = async (url, options = {}) => {
  // 完整URL
  const fullUrl = url.startsWith('http') ? url : `${BASE_URL}${url}`

  // 默认配置
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: TIME_OUT,
    credentials: 'include' // 携带cookies
  }

  // 合并配置
  const config = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers
    }
  }

  // 获取token (如果有)
  const token = getAuthToken()
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`
  }

  // 请求超时处理
  const controller = new AbortController()
  config.signal = controller.signal
  const timeout = setTimeout(() => controller.abort(), config.timeout)

  try {
    const response = await fetch(fullUrl, config)
    clearTimeout(timeout)

    // 检查HTTP状态码
    if (!response.ok) {
      throw new Error(`HTTP错误，状态: ${response.status}`)
    }

    // 解析响应
    const data = await response.json()

    // 业务状态码处理
    if (data.code !== 0 && data.code !== 200) {
      throw new Error(data.message || '请求失败')
    }

    // 如果有分页信息，返回完整数据；否则只返回data部分
    if (data.pagination) {
      return data
    }
    return data.data || data
  } catch (error) {
    clearTimeout(timeout)
    // 超时错误特殊处理
    if (error.name === 'AbortError') {
      throw new Error('请求超时')
    }
    throw error
  }
}

/**
 * 文件上传请求函数
 * @param {string} url 请求地址
 * @param {FormData} formData 表单数据
 * @param {Object} options 额外配置
 * @returns {Promise}
 */
export const uploadRequest = async (url, formData, options = {}) => {
  // 完整URL
  const fullUrl = url.startsWith('http') ? url : `${BASE_URL}${url}`

  // 文件上传配置（不设置Content-Type，让浏览器自动设置multipart/form-data）
  const config = {
    method: 'POST',
    body: formData,
    headers: {},
    timeout: TIME_OUT,
    credentials: 'include',
    ...options
  }

  // 获取token (如果有)
  const token = getAuthToken()
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`
  }

  // 请求超时处理
  const controller = new AbortController()
  config.signal = controller.signal
  const timeout = setTimeout(() => controller.abort(), config.timeout)

  try {
    const response = await fetch(fullUrl, config)
    clearTimeout(timeout)

    // 检查HTTP状态码
    if (!response.ok) {
      throw new Error(`HTTP错误，状态: ${response.status}`)
    }

    // 解析响应
    const data = await response.json()

    // 业务状态码处理
    if (data.code !== 0 && data.code !== 200) {
      throw new Error(data.message || '上传失败')
    }

    return data.data || data
  } catch (error) {
    clearTimeout(timeout)
    // 超时错误特殊处理
    if (error.name === 'AbortError') {
      throw new Error('请求超时')
    }
    throw error
  }
}

// HTTP方法封装
export const http = {
  get: (url, params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    const fullUrl = queryString ? `${url}?${queryString}` : url
    return request(fullUrl, { method: 'GET' })
  },
  post: (url, data) =>
    request(url, {
      method: 'POST',
      body: JSON.stringify(data)
    }),
  put: (url, data) =>
    request(url, {
      method: 'PUT',
      body: JSON.stringify(data)
    }),
  delete: (url, params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    const fullUrl = queryString ? `${url}?${queryString}` : url
    return request(fullUrl, { method: 'DELETE' })
  },
  
  // 文件上传方法
  upload: (url, formData, options = {}) => {
    return uploadRequest(url, formData, options)
  }
}

// 导出BASE_URL和getAuthToken供其他地方使用
export { BASE_URL, getAuthToken }
