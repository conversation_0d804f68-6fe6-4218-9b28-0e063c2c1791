/**
 * 应用常量配置
 */

// 默认头像路径
const DEFAULT_AVATAR = '/default.png'

// 文件上传配置
const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_AVATAR_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
  ALLOWED_AVATAR_TYPES: ['image/jpeg', 'image/jpg', 'image/png'],
  MAX_PHOTOS_COUNT: 6,
  MAX_POST_IMAGES: 9
}

// API响应状态码
const RESPONSE_CODES = {
  SUCCESS: 0,
  SUCCESS_ALT: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  VALIDATION_ERROR: 2001,
  RATE_LIMIT: 429,
  SERVER_ERROR: 500
}

// JWT配置
const JWT_CONFIG = {
  SECRET: process.env.JWT_SECRET || 'your-secret-key',
  EXPIRES_IN: '7d'
}

module.exports = {
  DEFAULT_AVATAR,
  UPLOAD_CONFIG,
  RESPONSE_CODES,
  JWT_CONFIG
} 