<template>
  <page-layout title="发布动态" :show-back="true">
    <template #header-right>
      <div class="header-right-container">
        <div v-if="isPrivate" class="privacy-indicator">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <rect x="3" y="11" width="18" height="11" rx="2"
ry="2" />
            <path d="M7 11V7a5 5 0 0 1 10 0v4" />
          </svg>
        </div>
        <div
          class="header-publish-btn"
          :class="{ 'btn-active': isValid && !isPublishing, 'btn-publishing': isPublishing }"
          @click="publishPost"
        >
          {{ isPublishing ? '发布中...' : '发布' }}
        </div>
      </div>
    </template>
    <div class="create-post-container">
      <!-- 文本输入区域 -->
      <div class="text-input-area">
        <textarea
          ref="textareaRef"
          v-model="postContent"
          placeholder="分享你的想法..."
          class="post-textarea"
          @input="adjustTextareaHeight"
        />
      </div>

      <!-- 已选图片预览 -->
      <div v-if="selectedImages.length > 0" class="selected-images">
        <div v-for="(image, index) in selectedImages" :key="index" class="image-preview-item">
          <img v-lazy="image.url" :alt="`预览图${index + 1}`" />
          <div class="image-delete" @click="removeImage(index)">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18" />
              <line x1="6" y1="6" x2="18" y2="18" />
            </svg>
          </div>
        </div>
      </div>

      <!-- 帖子预览 -->
      <div v-if="showPreview" class="post-preview-overlay" @click="togglePreview">
        <div class="post-preview-container" @click.stop>
          <div class="preview-header">
            <h3>帖子预览</h3>
            <div class="preview-close" @click="togglePreview">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <line x1="18" y1="6" x2="6" y2="18" />
                <line x1="6" y1="6" x2="18" y2="18" />
              </svg>
            </div>
          </div>
          <div class="preview-content">
            <div class="preview-card">
              <!-- 用户信息 -->
              <div class="preview-user-info">
                <div class="preview-avatar">
                  <avatar :src="userStore.userInfo.avatar || '/default.png'" size="medium" />
                </div>
                <div>
                  <div class="preview-username">{{ userStore.userInfo.nickname || userStore.userInfo.username || '用户' }}</div>
                  <div class="preview-meta">
                    <span>刚刚</span>
                    <span v-if="location" class="preview-location">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="12"
                        height="12"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
                        <circle cx="12" cy="10" r="3" />
                      </svg>
                      {{ location }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 帖子内容 -->
              <div v-if="postContent" class="preview-post-content">
                {{ postContent }}
              </div>

              <!-- 帖子图片 -->
              <div
                v-if="selectedImages.length > 0"
                class="preview-images"
                :class="previewImageLayoutClass"
              >
                <div
                  v-for="(image, index) in selectedImages"
                  :key="index"
                  class="preview-image-item"
                >
                  <img v-lazy="image.url" :alt="`图片${index + 1}`" />
                </div>
              </div>

              <!-- 隐私标记 -->
              <div v-if="isPrivate" class="preview-privacy">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <rect x="3" y="11" width="18" height="11" rx="2"
ry="2" />
                  <path d="M7 11V7a5 5 0 0 1 10 0v4" />
                </svg>
                <span>私密</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能按钮区域 -->
      <div class="function-area">
        <div class="function-buttons">
          <!-- 添加图片按钮 -->
          <div class="function-button" @click="triggerImageUpload">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="22"
              height="22"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <rect x="3" y="3" width="18" height="18" rx="2"
ry="2" />
              <circle cx="8.5" cy="8.5" r="1.5" />
              <polyline points="21 15 16 10 5 21" />
            </svg>
            <input
              ref="imageInputRef"
              type="file"
              accept="image/*"
              multiple
              style="display: none"
              @change="handleImageSelect"
            />
          </div>

          <!-- 位置按钮 -->
          <div class="function-button" @click="toggleLocationSelect">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="22"
              height="22"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
              <circle cx="12" cy="10" r="3" />
            </svg>
          </div>

          <!-- 话题按钮 -->
          <div class="function-button" @click="toggleTopicSelect">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="22"
              height="22"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
              <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
            </svg>
          </div>

          <!-- 隐私设置按钮 -->
          <div
            class="function-button"
            :class="{ 'function-button-active': isPrivate }"
            @click="togglePrivacy"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="22"
              height="22"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <rect x="3" y="11" width="18" height="11" rx="2"
ry="2" />
              <path d="M7 11V7a5 5 0 0 1 10 0v4" />
            </svg>
          </div>

          <!-- 预览按钮 -->
          <div class="function-button" @click="togglePreview">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="22"
              height="22"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
              <circle cx="12" cy="12" r="3" />
            </svg>
          </div>
        </div>

        <!-- 位置信息 -->
        <div v-if="location" class="location-info">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
            <circle cx="12" cy="10" r="3" />
          </svg>
          <span>{{ location }}</span>
          <div class="location-delete" @click="removeLocation">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="14"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18" />
              <line x1="6" y1="6" x2="18" y2="18" />
            </svg>
          </div>
        </div>

        <!-- 隐私设置信息 -->
        <div v-if="isPrivate" class="privacy-info">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <rect x="3" y="11" width="18" height="11" rx="2"
ry="2" />
            <path d="M7 11V7a5 5 0 0 1 10 0v4" />
          </svg>
          <span>仅自己可见</span>
          <div class="privacy-delete" @click="togglePrivacy">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="14"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18" />
              <line x1="6" y1="6" x2="18" y2="18" />
            </svg>
          </div>
        </div>
      </div>

      <!-- 位置选择面板 -->
      <div v-if="showLocationPanel" class="panel location-panel">
        <div class="panel-header">
          <div class="panel-title">选择位置</div>
          <div class="panel-close" @click="toggleLocationSelect">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18" />
              <line x1="6" y1="6" x2="18" y2="18" />
            </svg>
          </div>
        </div>
        <div class="panel-content">
          <div class="location-list">
            <div
              v-for="(loc, index) in locationOptions"
              :key="index"
              class="location-item"
              @click="selectLocation(loc)"
            >
              <div class="location-icon">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
                  <circle cx="12" cy="10" r="3" />
                </svg>
              </div>
              <div class="location-name">{{ loc }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 话题选择面板 -->
      <div v-if="showTopicPanel" class="panel topic-panel">
        <div class="panel-header">
          <div class="panel-title">选择话题</div>
          <div class="panel-close" @click="toggleTopicSelect">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18" />
              <line x1="6" y1="6" x2="18" y2="18" />
            </svg>
          </div>
        </div>
        <div class="panel-content">
          <div class="topic-search">
            <input v-model="topicSearch" type="text" placeholder="搜索话题" />
          </div>
          <div class="topic-list">
            <div
              v-for="(topic, index) in filteredTopics"
              :key="index"
              class="topic-item"
              @click="selectTopic(topic)"
            >
              <div class="topic-icon">#</div>
              <div class="topic-name">{{ topic }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </page-layout>
</template>

<script setup>
  import { ref, computed, onMounted, nextTick, inject } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/userStore.js'
import PageLayout from '../components/PageLayout.vue'
import Avatar from '../components/Avatar.vue'
  import { postService } from '../services/postService.js'
  import { uploadService } from '../services/uploadService.js'

  const router = useRouter()
  const userStore = useUserStore()
  const toast = inject('toast')

  // 帖子内容
  const postContent = ref('')
  const textareaRef = ref(null)

  // 隐私设置
  const isPrivate = ref(false)

  // 已选图片
  const selectedImages = ref([])
  const imageInputRef = ref(null)

  // 位置信息
  const location = ref('')
  const showLocationPanel = ref(false)
  const locationOptions = [
    '北京市朝阳区',
    '上海市浦东新区',
    '广州市天河区',
    '深圳市南山区',
    '杭州市西湖区',
    '成都市锦江区',
    '重庆市渝中区',
    '武汉市武昌区'
  ]

  // 话题信息
  const showTopicPanel = ref(false)
  const topicSearch = ref('')
  const topicOptions = [
    '旅行',
    '美食',
    '摄影',
    '电影',
    '音乐',
    '阅读',
    '健身',
    '时尚',
    '科技',
    '游戏'
  ]

  // 过滤后的话题列表
  const filteredTopics = computed(() => {
    if (!topicSearch.value) {
      return topicOptions
    }
    return topicOptions.filter(topic =>
      topic.toLowerCase().includes(topicSearch.value.toLowerCase())
    )
  })

  // 表单是否有效
  const isValid = computed(() => {
    return postContent.value.trim().length > 0 || selectedImages.value.length > 0
  })

  // 调整文本框高度
  const adjustTextareaHeight = () => {
    if (!textareaRef.value) {
      return
    }

    textareaRef.value.style.height = 'auto'
    textareaRef.value.style.height = `${textareaRef.value.scrollHeight}px`
  }

  // 触发图片上传
  const triggerImageUpload = () => {
    // 限制最多上传9张图片
    if (selectedImages.value.length >= 9) {
      alert('最多只能上传9张图片')
      return
    }

    imageInputRef.value.click()
  }

  // 处理图片选择
  const handleImageSelect = event => {
    const files = event.target.files
    if (!files || files.length === 0) {
      return
    }

    // 检查是否超过限制
    if (selectedImages.value.length + files.length > 9) {
      alert(`最多只能上传9张图片，当前已选择${selectedImages.value.length}张`)
      return
    }

    // 处理选中的图片
    Array.from(files).forEach(file => {
      // 检查文件类型
      if (!file.type.startsWith('image/')) {
        alert('请选择图片文件')
        return
      }

      // 使用FileReader预览图片
      const reader = new FileReader()
      reader.onload = e => {
        selectedImages.value.push({
          file,
          url: e.target.result
        })
      }
      reader.readAsDataURL(file)
    })

    // 重置文件输入
    event.target.value = ''
  }

  // 移除图片
  const removeImage = index => {
    selectedImages.value.splice(index, 1)
  }

  // 切换位置选择面板
  const toggleLocationSelect = () => {
    showLocationPanel.value = !showLocationPanel.value
    showTopicPanel.value = false
  }

  // 选择位置
  const selectLocation = loc => {
    location.value = loc
    showLocationPanel.value = false
  }

  // 移除位置
  const removeLocation = () => {
    location.value = ''
  }

  // 切换话题选择面板
  const toggleTopicSelect = () => {
    showTopicPanel.value = !showTopicPanel.value
    showLocationPanel.value = false
  }

  // 选择话题
  const selectTopic = topic => {
    // 在光标位置插入话题
    const textarea = textareaRef.value
    const startPos = textarea.selectionStart
    const endPos = textarea.selectionEnd

    const beforeText = postContent.value.substring(0, startPos)
    const afterText = postContent.value.substring(endPos)

    postContent.value = `${beforeText} #${topic}# ${afterText}`

    // 关闭面板
    showTopicPanel.value = false

    // 设置光标位置
    nextTick(() => {
      const newCursorPos = startPos + topic.length + 3 // +3 是因为添加了 " #" 和 "# "
      textarea.focus()
      textarea.setSelectionRange(newCursorPos, newCursorPos)

      // 调整高度
      adjustTextareaHeight()
    })
  }

  // 发布按钮
  const isPublishing = ref(false)

  // 发布帖子
  const publishPost = async () => {
    if (!isValid.value || isPublishing.value) {
      return
    }

    try {
      // 设置发布中状态
      isPublishing.value = true

      // 先上传图片
      const imageUrls = []
      for (const image of selectedImages.value) {
        try {
          // 验证文件
          uploadService.validateFile(image.file)
          
          const uploadResult = await uploadService.uploadFile(image.file, 'post')
          if (uploadResult && uploadResult.url) {
            imageUrls.push(uploadResult.url)
          }
        } catch (error) {
          toast.error('图片上传失败')
          throw error
        }
      }

      // 收集表单数据
      const postData = {
        content: postContent.value,
        images: imageUrls,
        location: location.value,
        privacy: isPrivate.value ? 'private' : 'public'
      }

      // 发布帖子
      const response = await postService.createPost(postData)
      
      if (response && response.id) {
        toast.success('发布成功')
        
        // 清空表单
        resetForm()
        
        // 返回到广场页面
        setTimeout(() => {
          router.push('/square')
        }, 800)
      }
    } catch (error) {
      toast.error('发布失败，请重试')
    } finally {
      isPublishing.value = false
    }
  }

  // 切换隐私设置
  const togglePrivacy = () => {
    isPrivate.value = !isPrivate.value
  }

  // 组件挂载时
  onMounted(() => {
    // 自动聚焦文本框
    textareaRef.value.focus()
  })

  // 帖子预览
  const showPreview = ref(false)
  const togglePreview = () => {
    showPreview.value = !showPreview.value
  }

  // 预览图片布局
  const previewImageLayoutClass = computed(() => {
    const count = selectedImages.value.length
    if (count === 1) {
      return 'single-image'
    }
    if (count === 2) {
      return 'double-image'
    }
    if (count === 3) {
      return 'triple-image'
    }
    if (count === 4) {
      return 'quad-image'
    }
    return 'multi-image'
  })
</script>

<style scoped>
  .create-post-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 15px 0 80px;
  }

  .text-input-area {
    padding: 16px;
    background-color: #fff;
    border-radius: 12px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .post-textarea {
    width: 100%;
    min-height: 120px;
    border: none;
    resize: none;
    font-size: 16px;
    line-height: 1.5;
    color: #333;
    outline: none;
    padding: 0;
  }

  .post-textarea::placeholder {
    color: #999;
  }

  .selected-images {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    padding: 0 16px;
    margin-bottom: 15px;
  }

  .image-preview-item {
    position: relative;
    padding-bottom: 100%;
    border-radius: 8px;
    overflow: hidden;
  }

  .image-preview-item img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .image-delete {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;
  }

  .function-area {
    padding: 12px 16px;
    background-color: #fff;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .function-buttons {
    display: flex;
    gap: 20px;
  }

  .function-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    cursor: pointer;
    transition: all 0.2s;
  }

  .function-button:hover {
    background-color: #eee;
  }

  .location-info {
    display: flex;
    align-items: center;
    margin-top: 12px;
    padding: 6px 12px;
    background-color: #f5f5f5;
    border-radius: 16px;
    font-size: 14px;
    color: #666;
    width: fit-content;
  }

  .location-info svg {
    margin-right: 5px;
    color: #ff6b6b;
  }

  .location-delete {
    margin-left: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }

  .panel {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 60vh;
    display: flex;
    flex-direction: column;
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .panel-title {
    font-size: 16px;
    font-weight: 600;
  }

  .panel-close {
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }

  .panel-close:hover {
    background-color: #f5f5f5;
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
  }

  .location-list,
  .topic-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .location-item,
  .topic-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-radius: 8px;
    cursor: pointer;
  }

  .location-item:hover,
  .topic-item:hover {
    background-color: #f5f5f5;
  }

  .location-icon,
  .topic-icon {
    margin-right: 12px;
    color: #ff6b6b;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .topic-icon {
    font-size: 18px;
    font-weight: bold;
  }

  .topic-search {
    margin-bottom: 16px;
  }

  .topic-search input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #eee;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
  }

  .publish-button-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background-color: #fff;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
  }

  .publish-button {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 8px;
    background-color: #ff6b6b;
    color: #fff;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }

  .publish-button:hover {
    background-color: #ff5252;
  }

  .button-disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }

  .button-disabled:hover {
    background-color: #ccc;
  }

  .button-publishing {
    background-color: #ff5252;
    cursor: not-allowed;
  }

  .button-publishing:hover {
    background-color: #ff5252;
  }

  .publishing-text {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .loading-dots {
    animation: blink 1.5s infinite;
    margin-right: 8px;
  }

  @keyframes blink {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  .header-publish-btn {
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 15px;
    font-weight: 600;
    color: #999;
    cursor: not-allowed;
    transition: all 0.2s;
    background-color: #f5f5f5;
  }

  .btn-active {
    color: #fff;
    background-color: #ff6b6b;
    cursor: pointer;
  }

  .btn-active:active {
    opacity: 0.8;
  }

  .btn-publishing {
    color: #fff;
    background-color: #ff9999;
    opacity: 0.8;
    cursor: not-allowed;
  }

  .function-button-active {
    background-color: #ff6b6b;
    color: #fff;
  }

  .privacy-info {
    display: flex;
    align-items: center;
    margin-top: 12px;
    padding: 6px 12px;
    background-color: #f5f5f5;
    border-radius: 16px;
    font-size: 14px;
    color: #666;
    width: fit-content;
  }

  .privacy-info svg {
    margin-right: 5px;
    color: #ff6b6b;
  }

  .privacy-delete {
    margin-left: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }

  .header-right-container {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .privacy-indicator {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .post-preview-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .post-preview-container {
    background-color: #fff;
    border-radius: 12px;
    width: 80%;
    max-height: 80vh;
    overflow: auto;
  }

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .preview-close {
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }

  .preview-close:hover {
    background-color: #f5f5f5;
  }

  .preview-content {
    padding: 16px;
  }

  .preview-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .preview-user-info {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  .preview-avatar {
    margin-right: 12px;
    width: 40px;
    height: 40px;
  }

  .preview-username {
    font-size: 16px;
    font-weight: 600;
  }

  .preview-meta {
    font-size: 14px;
    color: #999;
  }

  .preview-post-content {
    margin-bottom: 16px;
  }

  .preview-images {
    display: grid;
    gap: 8px;
    margin-bottom: 16px;
    border-radius: 8px;
    overflow: hidden;
  }

  .preview-images.single-image {
    grid-template-columns: 1fr;
  }

  .preview-images.single-image .preview-image-item {
    padding-bottom: 66.67%;
  }

  .preview-images.double-image {
    grid-template-columns: repeat(2, 1fr);
  }

  .preview-images.triple-image {
    grid-template-columns: repeat(3, 1fr);
  }

  .preview-images.quad-image {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
  }

  .preview-images.multi-image {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: auto;
  }

  .preview-image-item {
    position: relative;
    padding-bottom: 100%;
    border-radius: 8px;
    overflow: hidden;
  }

  .preview-image-item img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .preview-privacy {
    display: flex;
    align-items: center;
    margin-top: 16px;
    padding: 6px 12px;
    background-color: #f5f5f5;
    border-radius: 8px;
  }

  .preview-privacy svg {
    margin-right: 8px;
    color: #ff6b6b;
  }

  .preview-privacy span {
    font-size: 14px;
    color: #666;
  }
</style>
