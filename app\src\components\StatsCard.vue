<template>
  <div class="stats-card">
    <div v-for="(item, index) in items" :key="index" class="stat-item" @click="$emit('click', item)">
      <div class="stat-value">{{ item.value }}</div>
      <div class="stat-label">{{ item.label }}</div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  items: {
    type: Array,
    required: true
    // 每个项目应该有 value 和 label 属性
    // 例如: [{ value: 128, label: '访客' }, ...]
  }
})

defineEmits(['click'])
</script>

<style scoped>
.stats-card {
  display: flex;
  background-color: #fff;
  border-radius: var(--border-radius);
  overflow: hidden;
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-card);
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: var(--spacing-md) 0;
  position: relative;
  cursor: pointer;
}

.stat-item:not(:last-child):after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1px;
  background-color: var(--color-background);
}

.stat-value {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--font-color-light);
}
</style>
