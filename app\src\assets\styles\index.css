/* 统一样式文件 - 移动端全局样式 */

/* ==================== 全局主题变量和基础样式 ==================== */

/* 主题变量 */
:root {
  /* 字体 */
  --font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans',
    'Helvetica Neue', sans-serif;

  /* 颜色系统 */
  --font-color-primary: #222;
  --font-color-secondary: #444;
  --font-color-tertiary: #666;
  --font-color-light: #888;

  /* 主题色 */
  --color-primary: #ff5864;
  --color-primary-gradient: #ff8c7f;
  --color-primary-light: rgba(255, 88, 100, 0.15);
  --color-primary-dark: #ff4050;

  /* 功能色 */
  --color-success: #52c41a;
  --color-warning: #faad14;
  --color-error: #ff4d4f;
  --color-info: #1890ff;

  /* 背景色 */
  --color-background: #f5f7fa;
  --color-card-background: rgba(255, 255, 255, 0.95);
  --color-border: rgba(0, 0, 0, 0.12);

  /* 阴影 */
  --shadow-card: 0 10px 30px rgba(0, 0, 0, 0.15);
  --shadow-button: 0 7px 14px rgba(255, 88, 100, 0.25);

  /* 圆角 */
  --border-radius: 12px;
  --border-radius-sm: 8px;
  --border-radius-lg: 16px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 过渡时间 */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;

  /* Z-index层级管理 */
  --z-index-header: 100;
  --z-index-modal: 1000;
  --z-index-toast: 2000;

  /* 统一字体大小 */
  --font-size-xs: 10px;
  --font-size-sm: 12px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-xxl: 20px;

  /* 统一高度 */
  --height-header: 68px;
  --height-tabbar: 68px;
  --height-footer: 68px;
}

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

html,
body {
  width: 100%;
  height: 100%;
  font-size: var(--font-size-lg);
  color: var(--font-color-primary);
  line-height: 1.6;
  font-family: var(--font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden;
}

/* 适配iPhone X等机型的安全区域 */
@supports (padding-top: constant(safe-area-inset-top)) {
  body {
    padding-top: constant(safe-area-inset-top);
    padding-left: constant(safe-area-inset-left);
    padding-right: constant(safe-area-inset-right);
    padding-bottom: constant(safe-area-inset-bottom);
  }
}

@supports (padding-top: env(safe-area-inset-top)) {
  body {
    padding-top: env(safe-area-inset-top);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* 去掉列表样式 */
ul,
ol {
  list-style: none;
}

/* 去掉链接默认样式 */
a {
  text-decoration: none;
  color: inherit;
}

/* 图片自适应 */
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
  border-style: none;
}

/* 禁止长按菜单 */
a,
img {
  -webkit-touch-callout: none;
}

/* input相关 */
input,
textarea,
button {
  border: none;
  outline: none;
  background: none;
  font-family: var(--font-family);
}

/* 修复移动端点击延迟300ms */
html {
  touch-action: manipulation;
}

/* 防止页面滑动穿透 */
.no-scroll {
  overflow: hidden;
  position: fixed;
  width: 100%;
}

/* 文本工具类 */
/* 单行文本溢出省略 */
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 多行文本溢出省略 */
.ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
}

.ellipsis-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  overflow: hidden;
}

/* 文本色彩 */
.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--font-color-secondary);
}

.text-light {
  color: var(--font-color-light);
}

.text-success {
  color: var(--color-success);
}

.text-warning {
  color: var(--color-warning);
}

.text-error {
  color: var(--color-error);
}

/* 字重 */
.font-weight-bold {
  font-weight: 600;
}

.font-weight-medium {
  font-weight: 500;
}

.letter-spacing {
  letter-spacing: 0.5px;
}

/* 布局工具类 */
/* 铺满容器的绝对定位 */
.absolute-full {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

/* 清除浮动 */
.clearfix::after {
  content: '';
  display: block;
  clear: both;
}

/* Flexbox布局工具类 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-grow {
  flex-grow: 1;
}

/* 间距工具类 */
.mt-1 {
  margin-top: var(--spacing-xs);
}
.mt-2 {
  margin-top: var(--spacing-sm);
}
.mt-3 {
  margin-top: var(--spacing-md);
}
.mt-4 {
  margin-top: var(--spacing-lg);
}
.mt-5 {
  margin-top: var(--spacing-xl);
}

.mb-1 {
  margin-bottom: var(--spacing-xs);
}
.mb-2 {
  margin-bottom: var(--spacing-sm);
}
.mb-3 {
  margin-bottom: var(--spacing-md);
}
.mb-4 {
  margin-bottom: var(--spacing-lg);
}
.mb-5 {
  margin-bottom: var(--spacing-xl);
}

/* 页面容器 */
.page-container {
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  background-color: var(--color-background);
}

/* 底部安全区适配 */
.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 文本样式 */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-family);
  color: var(--font-color-primary);
  font-weight: 600;
  line-height: 1.4;
}

p {
  margin-bottom: 1rem;
  color: var(--font-color-secondary);
}

/* 性能优化相关样式 */
.gpu-accelerate {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
}

/* ==================== 动画样式库 ==================== */

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* 渐显动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-animation {
  animation: fadeIn 1s ease-out forwards;
  opacity: 0;
}

/* 从左滑入 */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.8s ease-out forwards;
  opacity: 0;
}

/* 从右滑入 */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out forwards;
  opacity: 0;
}

/* 从下滑入 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-up {
  animation: slideInUp 0.5s ease-out forwards;
  opacity: 0;
}

/* 旋转动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.spin-animation {
  animation: spin 1s linear infinite;
}

/* 链接悬停动画 */
.link-animation {
  position: relative;
  display: inline-block;
}

.link-animation::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: var(--color-primary);
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s;
}

.link-animation:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

/* 动画延迟类 */
.delay-1 {
  animation-delay: 0.3s;
}

.delay-2 {
  animation-delay: 0.6s;
}

.delay-3 {
  animation-delay: 0.9s;
}

.delay-4 {
  animation-delay: 1.2s;
}

.delay-5 {
  animation-delay: 1.5s;
}

/* ==================== 表单样式库 ==================== */

/* 表单容器 */
.form {
  width: 100%;
}

/* 表单项 */
.form-item,
.form-group {
  margin-bottom: 20px;
}

.form-group:last-child {
  margin-bottom: 0;
}

/* 表单标签 */
.form-label {
  display: block;
  font-size: var(--font-size-md);
  color: var(--font-color-secondary);
  margin-bottom: 8px;
  font-weight: 500;
}

/* 输入框包装器 */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background-color: rgba(255, 255, 255, 0.9);
  padding: 0 16px;
  transition: all 0.3s;
  overflow: hidden;
}

.input-wrapper:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

/* 输入框图标 */
.input-icon {
  color: #777;
  margin-right: 12px;
}

/* 输入框 */
.form-input,
.form-item input {
  width: 100%;
  padding: 12px 0;
  font-size: var(--font-size-lg);
  outline: none;
  border: none;
  background: transparent;
  color: var(--font-color-primary);
  font-weight: 500;
  font-family: var(--font-family);
}

.form-input::placeholder,
.form-item input::placeholder {
  color: var(--font-color-light);
  font-weight: 400;
}

/* 文本区域包装器 */
.textarea-wrapper {
  position: relative;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background-color: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  transition: all 0.3s;
  margin-bottom: 8px;
}

.textarea-wrapper:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

/* 文本区域 */
.form-textarea,
.textarea-wrapper textarea {
  width: 100%;
  padding: 16px;
  font-size: var(--font-size-lg);
  outline: none;
  border: none;
  background: transparent;
  color: var(--font-color-primary);
  resize: none;
  min-height: 100px;
  font-weight: 500;
  font-family: var(--font-family);
}

.form-textarea::placeholder,
.textarea-wrapper textarea::placeholder {
  color: var(--font-color-light);
  font-weight: 400;
}

/* 字符计数 */
.char-count {
  position: absolute;
  bottom: 8px;
  right: 12px;
  font-size: var(--font-size-xs);
  color: var(--font-color-light);
}

/* 选择框包装器 */
.select-wrapper {
  position: relative;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background-color: rgba(255, 255, 255, 0.9);
  overflow: hidden;
}

/* 选择框 */
.form-select {
  width: 100%;
  padding: 16px;
  appearance: none;
  border: none;
  outline: none;
  background: transparent;
  font-size: var(--font-size-lg);
  color: var(--font-color-primary);
  font-weight: 500;
  font-family: var(--font-family);
}

/* 选择框图标 */
.select-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  stroke: #777;
}

/* 选择器布局 */
.date-selector {
  display: grid;
  grid-template-columns: 1.2fr 1fr 1fr;
  gap: 10px;
  margin-bottom: 16px;
}

.region-selector {
  display: flex;
  gap: 10px;
}

/* 选项按钮 */
.option-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 12px;
  background-color: var(--color-background);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  color: var(--font-color-secondary);
}

.option-btn.active {
  background-color: var(--color-primary);
  color: #fff;
}

/* 性别选择 */
.gender-options {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.gender-option {
  flex: 1;
  padding: 16px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s;
  color: var(--font-color-secondary);
  background-color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.gender-option.active {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
  transform: translateY(-5px);
  box-shadow: 0 10px 20px var(--color-primary-light);
}

.gender-option.active svg {
  stroke: white;
}

.gender-option svg {
  stroke: var(--font-color-secondary);
  transition: all 0.3s;
}

/* 标签/兴趣选择 */
.interests-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 16px;
}

.interest-tag {
  padding: 8px 16px;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid var(--color-border);
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  user-select: none;
  color: var(--font-color-secondary);
  font-weight: 500;
}

.interest-tag:hover {
  background-color: var(--color-primary-light);
  border-color: rgba(255, 88, 100, 0.3);
}

.interest-tag.active {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--color-primary-light);
}

/* 提示文本 */
.hint-text,
.bio-hint,
.nickname-hint,
.birthday-hint,
.interests-hint {
  font-size: 12px;
  color: var(--font-color-light);
  margin-top: 8px;
}

/* 表单操作区 */
.form-actions {
  margin-top: 28px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
}

/* 按钮基础样式 */
.btn {
  width: 100%;
  padding: 16px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  font-family: var(--font-family);
}

/* 通用按钮样式 (for button elements) */
button {
  padding: 10px 16px;
  border: none;
  border-radius: var(--border-radius-sm);
  background-color: var(--color-info);
  color: white;
  cursor: pointer;
  font-size: var(--font-size-md);
  font-weight: 500;
  transition: all var(--transition-fast);
  font-family: var(--font-family);
}

button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

/* 主要按钮 */
.btn-primary,
.login-btn,
.register-btn,
.complete-btn,
.next-btn {
  background: linear-gradient(45deg, var(--color-primary), var(--color-primary-gradient));
  color: white;
  box-shadow: var(--shadow-button);
  letter-spacing: 1px;
}

.btn-primary:hover,
.login-btn:hover,
.register-btn:hover,
.complete-btn:hover,
.next-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 14px var(--color-primary-light);
}

.btn-primary:active,
.login-btn:active,
.register-btn:active,
.complete-btn:active,
.next-btn:active {
  transform: translateY(-1px);
}

/* 按钮内部元素 */
.btn-text {
  margin-right: 8px;
  transition: all 0.3s;
}

.btn-icon {
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s;
}

.btn:hover .btn-text {
  transform: translateX(-8px);
}

.btn:hover .btn-icon {
  opacity: 1;
  transform: translateX(0);
}

/* 次要按钮 */
.btn-secondary,
.back-btn,
.skip-btn {
  background: transparent;
  border: none;
  color: var(--font-color-secondary);
  font-size: 14px;
  font-weight: 500;
}

.back-btn {
  position: absolute;
  left: 0;
  top: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.back-btn:hover,
.skip-btn:hover {
  color: var(--color-primary);
}

/* ==================== 卡片和容器样式库 ==================== */

/* 页面容器基础样式 */
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  position: relative;
  overflow: hidden;
  font-family: var(--font-family);
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 20px 15px 70px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 背景形状容器 */
.background-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
}

/* 背景装饰形状 */
.shape {
  position: absolute;
  border-radius: 50%;
  opacity: 0.4;
}

.shape-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(45deg, var(--color-primary), var(--color-primary-gradient));
  top: -100px;
  right: -100px;
}

.shape-2 {
  width: 200px;
  height: 200px;
  background: linear-gradient(45deg, #84fab0, #8fd3f4);
  bottom: -50px;
  left: -50px;
}

.shape-3 {
  width: 150px;
  height: 150px;
  background: linear-gradient(45deg, #a18cd1, #fbc2eb);
  top: 100px;
  left: 30%;
}

/* 卡片基础样式 */
.card {
  background-color: var(--color-card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-card);
  padding: 16px;
  margin-bottom: 16px;
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--font-color-primary);
  margin-bottom: 12px;
  letter-spacing: 0.5px;
}

.card-content {
  color: var(--font-color-secondary);
  font-size: var(--font-size-md);
  line-height: 1.6;
}

/* 认证卡片样式 */
.auth-card {
  position: relative;
  width: 90%;
  max-width: 420px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  padding: 2rem;
  z-index: 1;
}

.login-card,
.register-card,
.complete-profile-card {
  position: relative;
  width: 90%;
  max-width: 420px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  padding: 2rem;
  z-index: 1;
}

.complete-profile-card {
  max-width: 480px;
  margin: 1rem 0;
  max-height: 90vh;
  overflow-y: auto;
}

/* 头部样式 */
.auth-header,
.login-header,
.register-header,
.profile-header {
  margin-bottom: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.logo {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.auth-header h2,
.login-header h2,
.register-header h2,
.profile-header h2 {
  color: var(--font-color-primary);
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  letter-spacing: 0.5px;
}

.auth-header p,
.login-header p,
.register-header p,
.profile-header p {
  color: var(--font-color-secondary);
  font-size: var(--font-size-lg);
}

/* 个人资料卡片样式 */
.profile-card {
  background-color: #fff;
  border-radius: var(--border-radius);
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

/* 栅格布局 */
.grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

@media (min-width: 768px) {
  .grid-container {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 375px) {
  .grid-container {
    gap: 10px;
  }
}

/* 空状态 */
.empty-state {
  padding: 40px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.empty-icon {
  font-size: 40px;
  margin-bottom: 10px;
  color: #ccc;
}

.empty-text {
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: var(--font-color-primary);
  margin-bottom: 8px;
  font-family: var(--font-family);
}

.empty-desc {
  font-size: var(--font-size-md);
  color: var(--font-color-light);
  font-family: var(--font-family);
}

/* 认证容器 */
.auth-container,
.login-container,
.register-container,
.complete-profile-container {
  position: relative;
  padding: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e9f2 100%);
  overflow: hidden;
  font-family: var(--font-family);
}

/* ==================== 视图组件共享样式 ==================== */

/* 头部样式 */
.header {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  height: var(--height-header);
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  z-index: var(--z-index-header);
}

.header h1 {
  font-size: var(--font-size-xxl);
  font-weight: 600;
  margin: 0;
  color: var(--color-primary);
  letter-spacing: 0.5px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-right {
  position: absolute;
  right: 16px;
  display: flex;
  align-items: center;
}

.header-left {
  position: absolute;
  left: 16px;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: var(--font-size-xxl);
  color: var(--font-color-secondary);
  cursor: pointer;
}

/* 底部导航栏 */
.tab-bar {
  height: var(--height-tabbar);
  border-top: 1px solid var(--color-border);
  display: flex;
  background-color: #fff;
  box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--font-color-light);
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}

.tab-item.active {
  color: var(--color-primary);
}

.tab-icon {
  margin-bottom: 3px;
  font-size: var(--font-size-xxl);
}

/* 底部区域 */
.footer {
  height: var(--height-footer);
}

/* 底部操作区固定样式 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 12px 16px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 50;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
}

/* 顶部标签页导航 */
.tab-container {
  position: relative;
  z-index: 20;
  margin-top: 10px;
  margin-bottom: 10px;
}

.tab-header {
  display: flex;
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 4px;
  position: relative;
  margin: 0 5px;
}

.tab-header-item {
  flex: 1;
  padding: 10px 0;
  text-align: center;
  font-size: 14px;
  position: relative;
  z-index: 2;
  transition: color var(--transition-fast);
  font-weight: 500;
}

.tab-header-item.active {
  color: var(--color-primary);
}

.tab-indicator {
  position: absolute;
  height: calc(100% - 8px);
  border-radius: 6px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform var(--transition-normal);
  top: 4px;
  z-index: 1;
}

/* 列表样式 */
.list-container {
  width: 100%;
  background-color: #fff;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.list-item {
  padding: 16px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--color-border);
  transition: background-color var(--transition-fast);
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background-color: #f5f5f5;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: var(--font-size-lg);
  color: var(--font-color-primary);
  font-weight: 500;
  margin-bottom: 4px;
}

.list-item-subtitle {
  font-size: var(--font-size-md);
  color: var(--font-color-light);
}

.list-item-right {
  margin-left: 10px;
  color: var(--font-color-light);
  display: flex;
  align-items: center;
}

/* 分割线 */
.divider {
  width: 100%;
  height: 1px;
  background-color: var(--color-border);
  margin: 12px 0;
}

/* 遮罩层 */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
}

/* 弹出层 */
.modal {
  width: 90%;
  max-width: 420px;
  background-color: #fff;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.modal-header {
  padding: 16px;
  border-bottom: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
}

.modal-close {
  font-size: var(--font-size-xxl);
  color: var(--font-color-light);
  cursor: pointer;
}

.modal-body {
  padding: 16px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 12px 16px;
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 页面滚动容器 */
.page-scroll {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

/* 加载状态 */
.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

/* 媒体查询 */
@media (max-width: 375px) {
  .content {
    padding-top: 15px;
  }
}

@media (min-width: 768px) {
  .content {
    padding-top: 30px;
  }
}
