<template>
  <div class="auth-container">
    <div class="background-shapes">
      <div class="shape shape-1" />
      <div class="shape shape-2" />
      <div class="shape shape-3" />
    </div>

    <div class="auth-card">
      <div class="auth-header">
        <div class="logo">
          <svg
            width="60"
            height="60"
            viewBox="0 0 24 24"
            fill="none"
            stroke="#ff5864"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path
              d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
            />
          </svg>
        </div>
        <h2>用户注册</h2>
        <p>创建账号，开始使用全部功能</p>
      </div>

      <div class="form">
        <div class="form-item">
          <div class="input-wrapper">
            <svg
              class="input-icon"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
              <circle cx="12" cy="7" r="4" />
            </svg>
            <input
id="username"
v-model="form.username" type="text"
placeholder="请输入用户名" />
          </div>
        </div>

        <div class="form-item">
          <div class="input-wrapper">
            <svg
              class="input-icon"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <rect x="3" y="11" width="18" height="11" rx="2"
ry="2" />
              <path d="M7 11V7a5 5 0 0 1 10 0v4" />
            </svg>
            <input
id="password"
v-model="form.password" type="password"
placeholder="请输入密码" />
          </div>
        </div>

        <div class="form-item">
          <div class="input-wrapper">
            <svg
              class="input-icon"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <rect x="3" y="11" width="18" height="11" rx="2"
ry="2" />
              <path d="M7 11V7a5 5 0 0 1 10 0v4" />
            </svg>
            <input
              id="confirmPassword"
              v-model="form.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
            />
          </div>
        </div>

        <div class="form-actions">
          <button class="btn btn-primary" @click="handleRegister">
            <span class="btn-text">注 册</span>
            <span class="btn-icon">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path d="M5 12h14" />
                <path d="m12 5 7 7-7 7" />
              </svg>
            </span>
          </button>
        </div>

        <div class="form-footer">
          <p>
            已有账号？
            <router-link to="/login">去登录</router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, inject } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { useUserStore } from '../stores/userStore'
  import { authService } from '../services/authService'

  const router = useRouter()
  const route = useRoute()
  const userStore = useUserStore()
  const toast = inject('toast')

  const form = ref({
    username: '',
    password: '',
    confirmPassword: ''
  })

  const handleRegister = async () => {
    try {
      if (!form.value.username || !form.value.password || !form.value.confirmPassword) {
        toast.warning('请填写完整的注册信息')
        return
      }

      if (form.value.password !== form.value.confirmPassword) {
        toast.error('两次输入的密码不一致')
        return
      }

      // 调用注册API
      const response = await authService.register({
        username: form.value.username,
        password: form.value.password,
        confirmPassword: form.value.confirmPassword
      })

      // 保存token和用户信息
      userStore.setToken(response.token)
      userStore.setUserInfo(response.userInfo)

      toast.success('注册成功')

      // 延迟跳转，让用户看到提示
      setTimeout(() => {
        // 如果用户资料未完善，跳转到完善资料页面，否则跳转到首页
        if (!response.userInfo.profileCompleted) {
          router.push('/complete-profile')
        } else {
          router.push('/')
        }
      }, 800)
    } catch (error) {
      toast.error(error.message || '注册失败，请稍后再试')
    }
  }
</script>

<style scoped>
  .form-footer {
    margin-top: 2rem;
    text-align: center;
    color: var(--font-color-secondary);
    font-size: 0.95rem;
  }

  .form-footer a {
    color: var(--color-primary);
    font-weight: 600;
  }
</style>
