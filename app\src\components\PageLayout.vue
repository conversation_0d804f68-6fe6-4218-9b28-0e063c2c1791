<template>
  <div class="page-container">
    <div class="header">
      <div class="header-left">
        <div v-if="showBack" class="back-button" @click="goBack">
          <svg class="icon-back" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
        </div>
        <slot name="title">
          <h1>{{ title }}</h1>
        </slot>
      </div>
      <div class="header-right">
        <slot name="header-right" />
      </div>
    </div>

    <div class="content">
      <slot />
    </div>

    <!-- 底部安全区域占位 -->
    <div v-if="!hideSafeArea" class="footer safe-area-bottom" />

    <!-- 底部导航栏 -->
    <tab-bar v-if="!hideTabBar" :items="tabItems" @center-click="handleCenterClick" />
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import TabBar from './TabBar.vue'
import { useTabBarStore } from '../stores/tabBarStore'

const router = useRouter()

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  showBack: {
    type: Boolean,
    default: false
  },
  hideTabBar: {
    type: Boolean,
    default: false
  },
  hideSafeArea: {
    type: Boolean,
    default: false
  }
})

// 返回上一页
const goBack = () => {
  router.back()
}

// 使用全局TabBar数据
const tabBarStore = useTabBarStore()
const tabItems = tabBarStore.items
const handleCenterClick = tabBarStore.handleCenterClick
</script>

<style scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #ffffff;
  overflow: hidden;
}

.header {
  position: relative;
  padding: 32px 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 30;
  background-color: #ffffff;
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
}

.back-button {
  margin-right: 12px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.03);
  transition: all 0.2s ease;
}

.back-button:hover {
  background-color: rgba(0, 0, 0, 0.08);
}

.back-button:active {
  transform: scale(0.92);
}

.icon-back {
  width: var(--font-size-xl);
  height: var(--font-size-xl);
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
}

.header h1 {
  font-size: var(--font-size-xxl);
  font-weight: 600;
  margin: 0;
  color: #333333;
}

.content {
  flex: 1;
  overflow-y: auto;
  padding: 10px 15px 0;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

.content::-webkit-scrollbar {
  display: none;
}

.footer {
  height: var(--height-footer);
  position: relative;
  z-index: 30;
  pointer-events: none;
}

/* 修复图标倾斜问题 */
:deep(svg),
:deep(i),
:deep(span[class*='icon']),
:deep(*[class*='icon']) {
  transform: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-style: normal !important;
  text-align: center !important;
  vertical-align: middle !important;
}

/* 特别针对emoji图标 */
:deep(*:has(emoji)),
:deep(span:has(emoji)),
:deep(i:has(emoji)) {
  font-family:
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji', sans-serif !important;
  font-style: normal !important;
  text-rendering: auto !important;
  -webkit-font-smoothing: antialiased !important;
}

/* 卡片样式 - 可用于内容中的子组件 */
:deep(.card) {
  background-color: #ffffff;
  border-radius: var(--border-radius);
  padding: 15px;
  margin-bottom: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
}

/* 按钮样式 - 基于探探扁平化风格 */
:deep(.button) {
  border-radius: 20px;
  padding: 8px 20px;
  font-weight: 500;
  border: none;
  background-color: var(--color-primary);
  color: white;
}
</style>
