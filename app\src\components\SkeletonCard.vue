<template>
  <div class="skeleton-card">
    <div class="skeleton-image" />
    <div class="skeleton-content">
      <div class="skeleton-header">
        <div class="skeleton-title" />
        <div class="skeleton-subtitle" />
      </div>
      <div class="skeleton-tags">
        <div class="skeleton-tag" />
        <div class="skeleton-tag" />
        <div class="skeleton-tag" />
      </div>
    </div>
  </div>
</template>

<script setup>
// 无需任何数据处理
</script>

<style scoped>
.skeleton-card {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius);
  background-color: #fff;
  box-shadow: var(--shadow-card);
  overflow: hidden;
  z-index: var(--z-index-header);
  animation: pulse var(--transition-slow) infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }

  100% {
    opacity: 1;
  }
}

.skeleton-image {
  width: 100%;
  height: 80%;
  background: linear-gradient(90deg,
      var(--color-background) 25%,
      #e0e0e0 50%,
      var(--color-background) 75%);
  background-size: 200% 100%;
  animation: shimmer var(--transition-slow) infinite;
}

.skeleton-content {
  padding: var(--spacing-md);
}

.skeleton-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.skeleton-title {
  width: 40%;
  height: var(--font-size-xl);
  border-radius: var(--border-radius-sm);
  background: linear-gradient(90deg,
      var(--color-background) 25%,
      #e0e0e0 50%,
      var(--color-background) 75%);
  background-size: 200% 100%;
  animation: shimmer var(--transition-slow) infinite;
}

.skeleton-subtitle {
  width: 25%;
  height: var(--font-size-md);
  border-radius: var(--border-radius-sm);
  background: linear-gradient(90deg,
      var(--color-background) 25%,
      #e0e0e0 50%,
      var(--color-background) 75%);
  background-size: 200% 100%;
  animation: shimmer var(--transition-slow) infinite;
}

.skeleton-tags {
  display: flex;
  flex-wrap: wrap;
}

.skeleton-tag {
  width: 60px;
  height: 24px;
  border-radius: 20px;
  margin-right: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  background: linear-gradient(90deg,
      var(--color-background) 25%,
      #e0e0e0 50%,
      var(--color-background) 75%);
  background-size: 200% 100%;
  animation: shimmer var(--transition-slow) infinite;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}
</style>
