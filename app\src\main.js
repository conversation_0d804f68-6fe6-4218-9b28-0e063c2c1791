import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import VueLazyload from 'vue-lazyload'
import ToastPlugin from './plugins/toast'
import { DEFAULT_AVATAR } from './config'
import './assets/styles/index.css'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(ToastPlugin)
app.use(VueLazyload, {
  error: DEFAULT_AVATAR,
  loading: DEFAULT_AVATAR,
  attempt: 1
})
app.mount('#app')
