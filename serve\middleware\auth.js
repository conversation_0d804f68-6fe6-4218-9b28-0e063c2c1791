const jwt = require('jsonwebtoken')
const { readData } = require('../utils/dataStore')

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

/**
 * JWT认证中间件
 */
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1] // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      code: 1001,
      message: '缺少认证token'
    })
  }

  jwt.verify(token, JWT_SECRET, async (err, decoded) => {
    if (err) {
      return res.status(401).json({
        code: 1002,
        message: 'token无效或已过期'
      })
    }

    try {
      // 验证用户是否存在
      const users = await readData('users')
      const user = users.find(u => u.id === decoded.userId)
      
      if (!user) {
        return res.status(401).json({
          code: 3001,
          message: '用户不存在'
        })
      }

      // 将用户信息添加到请求对象
      req.user = {
        id: user.id,
        username: user.username,
        avatar: user.avatar,
        nickname: user.nickname
      }
      
      next()
    } catch (error) {
      console.error('认证中间件错误:', error)
      return res.status(500).json({
        code: 500,
        message: '认证服务错误'
      })
    }
  })
}

/**
 * 生成JWT token
 */
function generateToken(userId) {
  return jwt.sign(
    { userId },
    JWT_SECRET,
    { expiresIn: '7d' } // 7天过期
  )
}

/**
 * 验证token（不作为中间件使用）
 */
function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET)
  } catch (error) {
    return null
  }
}

module.exports = {
  authenticateToken,
  generateToken,
  verifyToken
} 