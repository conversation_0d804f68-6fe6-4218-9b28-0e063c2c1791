<template>
  <transition name="panel">
    <div v-show="modelValue" ref="panelRef" class="emoji-panel">
      <div class="emoji-close-btn" @click="close">×</div>
      <div class="emoji-categories">
        <span v-for="(category, index) in categories" :key="index" class="category-item"
          :class="{ active: currentCategory === index }" @click="changeCategory(index)">
          {{ category.icon }}
        </span>
      </div>
      <div class="emoji-list">
        <span v-for="emoji in currentEmojis" :key="emoji" class="emoji-item" @click="selectEmoji(emoji)">
          {{ emoji }}
        </span>
      </div>
    </div>
  </transition>
</template>

<script setup>
import { computed, ref, onMounted, onBeforeUnmount, nextTick } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'select'])

// 添加面板引用
const panelRef = ref(null)

// 表情分类
const categories = [
  {
    icon: '😀',
    name: '表情',
    emojis: [
      '😀',
      '😃',
      '😄',
      '😁',
      '😆',
      '😅',
      '😂',
      '🙂',
      '🙃',
      '😉',
      '😊',
      '😇',
      '😍',
      '😘',
      '😗',
      '😙',
      '😚',
      '😋',
      '😛',
      '😜',
      '😝',
      '😎',
      '🤩',
      '😏',
      '😒',
      '😞',
      '😔',
      '😟',
      '😕',
      '😣',
      '😖',
      '😫',
      '😩',
      '😢',
      '😭',
      '😤',
      '😠',
      '😡',
      '😳',
      '😱',
      '😨',
      '😰',
      '😥',
      '😓',
      '🤗',
      '🤔',
      '🤐',
      '🤨',
      '😐',
      '😑'
    ]
  },
  {
    icon: '👍',
    name: '手势',
    emojis: [
      '👋',
      '🤚',
      '✋',
      '👌',
      '✌️',
      '👈',
      '👉',
      '👆',
      '👇',
      '☝️',
      '👍',
      '👎',
      '✊',
      '👊',
      '👏',
      '🙌',
      '👐',
      '🤲',
      '🙏',
      '✍️',
      '💪',
      '👂',
      '👃',
      '👀',
      '👁️',
      '👅',
      '👄',
      '💋',
      '👨',
      '👩',
      '🧑',
      '👧',
      '👦',
      '🧒',
      '👶',
      '👴',
      '👵',
      '🧓',
      '👱',
      '👮',
      '👷',
      '💂',
      '🕵️',
      '👨‍⚕️',
      '👩‍⚕️',
      '👨‍🎓',
      '👩‍🎓',
      '👨‍🏫',
      '👩‍🏫',
      '👨‍🍳'
    ]
  },
  {
    icon: '❤️',
    name: '符号',
    emojis: [
      '❤️',
      '🧡',
      '💛',
      '💚',
      '💙',
      '💜',
      '🖤',
      '💔',
      '💕',
      '💞',
      '💓',
      '💗',
      '💖',
      '💘',
      '💝',
      '💟',
      '☮️',
      '✝️',
      '☪️',
      '🕉️',
      '☸️',
      '✡️',
      '🔯',
      '🕎',
      '☯️',
      '☢️',
      '☣️',
      '📛',
      '🚫',
      '💯',
      '💢',
      '♨️',
      '🚷',
      '🚯',
      '🚳',
      '🚱',
      '🔞',
      '📵',
      '🚭',
      '❌',
      '⭕',
      '❗',
      '❕',
      '❓',
      '❔',
      '‼️',
      '⁉️',
      '💤',
      '💥',
      '💫'
    ]
  },
  {
    icon: '🐱',
    name: '动物',
    emojis: [
      '🐶',
      '🐱',
      '🐭',
      '🐹',
      '🐰',
      '🦊',
      '🐻',
      '🐼',
      '🐨',
      '🐯',
      '🦁',
      '🐮',
      '🐷',
      '🐽',
      '🐸',
      '🐵',
      '🙈',
      '🙉',
      '🙊',
      '🐒',
      '🐔',
      '🐧',
      '🐦',
      '🐤',
      '🐣',
      '🐥',
      '🦆',
      '🦅',
      '🦉',
      '🦇',
      '🐺',
      '🐗',
      '🐴',
      '🦄',
      '🐝',
      '🐛',
      '🦋',
      '🐌',
      '🐞',
      '🐜',
      '🦗',
      '🕷️',
      '🦂',
      '🦟',
      '🦠',
      '🐢',
      '🐍',
      '🦎',
      '🦖',
      '🦕'
    ]
  },
  {
    icon: '🍎',
    name: '食物',
    emojis: [
      '🍏',
      '🍎',
      '🍐',
      '🍊',
      '🍋',
      '🍌',
      '🍉',
      '🍇',
      '🍓',
      '🍒',
      '🍑',
      '🍍',
      '🥥',
      '🥝',
      '🍅',
      '🍆',
      '🥑',
      '🥦',
      '🥒',
      '🌽',
      '🥕',
      '🥔',
      '🍠',
      '🥐',
      '🍞',
      '🥖',
      '🥨',
      '🧀',
      '🥚',
      '🍳',
      '🥓',
      '🥩',
      '🍗',
      '🍖',
      '🌭',
      '🍔',
      '🍟',
      '🍕',
      '🥪',
      '🥙',
      '🌮',
      '🌯',
      '🥗',
      '🥘',
      '🥫',
      '🍝',
      '🍜',
      '🍲',
      '🍛',
      '🍣'
    ]
  }
]

// 当前选择的类别
const currentCategory = ref(0)

// 根据当前类别获取表情列表
const currentEmojis = computed(() => {
  return categories[currentCategory.value].emojis
})

// 切换类别
const changeCategory = index => {
  currentCategory.value = index
}

// 关闭面板
const close = () => {
  emit('update:modelValue', false)
}

// 选择表情
const selectEmoji = emoji => {
  emit('select', emoji)
  // 不再自动关闭面板，允许用户连续选择表情
}

// 点击外部区域关闭面板
const handleOutsideClick = event => {
  // 检查面板是否显示，以及点击是否在面板外部
  if (props.modelValue && panelRef.value && !panelRef.value.contains(event.target)) {
    // 检查点击的元素是否是触发表情面板的按钮
    const emojiTriggerElements = document.querySelectorAll('.tool-item')
    for (const element of emojiTriggerElements) {
      if (element.contains(event.target)) {
        return // 如果点击的是触发按钮，不关闭面板
      }
    }
    emit('update:modelValue', false)
  }
}

// 添加和移除事件监听器
onMounted(() => {
  // 使用nextTick确保DOM已更新后再添加事件监听
  nextTick(() => {
    // 使用捕获阶段监听，确保在冒泡之前处理
    document.addEventListener('click', handleOutsideClick, true)
  })
})

onBeforeUnmount(() => {
  document.removeEventListener('click', handleOutsideClick, true)
})
</script>

<style scoped>
.emoji-panel {
  position: absolute;
  bottom: 100%;
  /* 定位在父元素的上方 */
  left: 0;
  right: 0;
  height: 300px;
  margin-bottom: 10px;
  /* 添加底部间距 */
  background-color: #fff;
  border: 1px solid #f0f0f0;
  z-index: 30;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  will-change: transform;
  border-radius: 12px;
  overflow: hidden;
  padding-top: 10px;
  /* 添加顶部内边距 */
}

.emoji-close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  cursor: pointer;
  border-radius: 50%;
  background-color: #f5f5f5;
  z-index: 31;
}

.emoji-close-btn:hover {
  background-color: #e0e0e0;
}

.emoji-categories {
  display: flex;
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
  overflow-x: auto;
}

.category-item {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  border-radius: 8px;
  margin: 0 4px;
}

.category-item:hover {
  background-color: #f5f5f5;
}

.category-item.active {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

.emoji-list {
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
  overflow-y: auto;
  flex: 1;
}

.emoji-item {
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-radius: 8px;
}

.emoji-item:hover {
  background-color: #f5f5f5;
}

/* 过渡效果 */
.panel-enter-active,
.panel-leave-active {
  transition:
    transform 0.25s ease,
    opacity 0.25s ease;
}

.panel-enter-from,
.panel-leave-to {
  transform: translateY(15px);
  opacity: 0;
}
</style>
