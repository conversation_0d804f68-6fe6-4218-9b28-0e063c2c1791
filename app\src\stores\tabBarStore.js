import { defineStore } from 'pinia'

export const useTabBarStore = defineStore('tabBar', {
  state: () => ({
    items: [
      {
        text: '推荐',
        path: '/',
        icon: 'icon-home',
        activeIcon: 'icon-home-fill'
      },
      {
        text: '消息',
        path: '/message',
        icon: 'icon-message',
        activeIcon: 'icon-message-fill',
        badge: '99+'
      },
      {
        text: '喜欢',
        path: '/likes',
        icon: 'icon-heart',
        activeIcon: 'icon-heart-fill'
      },
      {
        text: '我的',
        path: '/mine',
        icon: 'icon-user',
        activeIcon: 'icon-user-fill',
        badge: 'dot'
      }
    ],
    isInSquare: false
  }),

  actions: {
    // 处理中心按钮点击
    handleCenterClick() {
      // 不能在这里使用useRouter，应该返回要导航的路径，让组件处理导航
      if (this.isInSquare) {
        return '/create-post'
      } else {
        return '/square'
      }
    },

    // 设置是否在广场页面
    setInSquare(status) {
      this.isInSquare = status
    }
  }
})
