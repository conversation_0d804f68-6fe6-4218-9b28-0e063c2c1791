<template>
  <div class="comment-item">
    <!-- 用户头像 -->
    <div class="comment-avatar" @click="navigateToProfile">
      <avatar :src="comment.avatar" size="small" />
    </div>

    <!-- 评论内容 -->
    <div class="comment-content">
      <div class="comment-header">
        <div class="comment-username" @click="navigateToProfile">{{ comment.nickname || comment.username }}</div>
        <div class="comment-time">{{ formatTime(comment.createTime) }}</div>
      </div>

      <!-- 回复目标 -->
      <div v-if="comment.replyTo" class="comment-reply-to">
        回复
        <span class="reply-username">@{{ comment.replyTo }}</span>
      </div>

      <!-- 评论文字 -->
      <div class="comment-text">{{ comment.content }}</div>

      <!-- 评论图片 -->
      <div v-if="comment.image" class="comment-image" @click="previewImage(comment.image)">
        <img v-lazy="comment.image" alt="评论图片" />
      </div>

      <!-- 评论操作 -->
      <div class="comment-actions">
        <div class="comment-action" @click="handleLike">
          <span :class="{ liked: comment.isLiked }">
            {{ comment.isLiked ? '已赞' : '赞' }}
          </span>
          <span v-if="comment.likeCount > 0">({{ comment.likeCount }})</span>
        </div>
        <div class="comment-action" @click="handleReply">回复</div>
        <div v-if="comment.isMine" class="comment-action" @click="handleDelete">删除</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import Avatar from './Avatar.vue'

const router = useRouter()

// 定义props
const props = defineProps({
  comment: {
    type: Object,
    required: true
  }
})

// 定义事件
const emit = defineEmits(['like', 'reply', 'delete', 'preview-image'])

// 格式化时间
const formatTime = date => {
  const now = new Date()
  const diff = now - date

  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }

  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 1000))}分钟前`
  }

  // 小于24小时
  if (diff < 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
  }

  // 小于30天
  if (diff < 30 * 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`
  }

  // 大于30天，显示具体日期
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
}

// 处理点赞
const handleLike = () => {
  emit('like', props.comment.id)
}

// 处理回复
const handleReply = () => {
  emit('reply', props.comment.id, props.comment.nickname || props.comment.username)
}

// 处理删除
const handleDelete = () => {
  if (confirm('确定要删除这条评论吗？')) {
    emit('delete', props.comment.id)
  }
}

// 导航到用户资料页
const navigateToProfile = () => {
  router.push(`/profile/${props.comment.userId}`)
}

// 预览图片
const previewImage = imageUrl => {
  emit('preview-image', imageUrl)
}
</script>

<style scoped>
.comment-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.comment-avatar {
  width: 36px;
  height: 36px;
  margin-right: 12px;
  flex-shrink: 0;
  cursor: pointer;
}

.comment-content {
  flex: 1;
  overflow: hidden;
}

.comment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.comment-username {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.comment-reply-to {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.reply-username {
  color: #1890ff;
}

.comment-text {
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  margin-bottom: 8px;
  word-break: break-word;
}

.comment-image {
  margin: 8px 0;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  max-width: 200px;
}

.comment-image img {
  width: 100%;
  display: block;
  object-fit: cover;
}

.comment-actions {
  display: flex;
  gap: 16px;
}

.comment-action {
  font-size: 13px;
  color: #666;
  cursor: pointer;
}

.comment-action:hover {
  color: #1890ff;
}

.liked {
  color: #ff6b6b;
}
</style>
