<template>
  <page-layout title="修改密码" :show-back="true" hide-tab-bar hide-safe-area>
    
    <template #header-right>
      <button class="confirm-btn" @click="handleSubmit" :disabled="isSubmitting">
        {{ isSubmitting ? '提交中...' : '确定' }}
      </button>
    </template>
    
    <div class="form-container">
      <!-- 当前密码 -->
      <div class="form-item">
        <label for="currentPassword">当前密码</label>
        <input
          id="currentPassword"
          v-model="formData.currentPassword"
          type="password"
          placeholder="请输入当前密码"
          class="form-input"
        />
      </div>

      <!-- 新密码 -->
      <div class="form-item">
        <label for="newPassword">新密码</label>
        <input
          id="newPassword"
          v-model="formData.newPassword"
          type="password"
          placeholder="请输入新密码"
          class="form-input"
        />
      </div>

      <!-- 确认新密码 -->
      <div class="form-item">
        <label for="confirmPassword">确认新密码</label>
        <input
          id="confirmPassword"
          v-model="formData.confirmPassword"
          type="password"
          placeholder="请再次输入新密码"
          class="form-input"
        />
      </div>

      <div class="form-tips">
        <p>密码要求：</p>
        <ul>
          <li>至少8个字符</li>
          <li>包含大小写字母和数字</li>
          <li>可以包含特殊字符</li>
        </ul>
      </div>
    </div>
  </page-layout>
</template>

<script setup>
import { ref, inject } from 'vue'
import { useRouter } from 'vue-router'
import PageLayout from '../components/PageLayout.vue'

const router = useRouter()
const toast = inject('toast')

const formData = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const isSubmitting = ref(false)

// 表单验证
const validateForm = () => {
  if (!formData.value.currentPassword) {
    toast.error('请输入当前密码')
    return false
  }

  if (!formData.value.newPassword) {
    toast.error('请输入新密码')
    return false
  }

  if (formData.value.newPassword.length < 8) {
    toast.error('新密码长度至少为8个字符')
    return false
  }

  if (!formData.value.confirmPassword) {
    toast.error('请确认新密码')
    return false
  }

  if (formData.value.confirmPassword !== formData.value.newPassword) {
    toast.error('两次输入的密码不一致')
    return false
  }

  return true
}

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  isSubmitting.value = true

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    toast.success('密码修改成功！')
    
    setTimeout(() => {
      router.push('/settings')
    }, 1500)
  } catch {
    toast.error('密码修改失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style scoped>
/* 表单区域 */
.form-container {
  padding: 30px 20px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-item label {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

/* 下划线输入框样式 */
.form-input {
  padding: 10px 0;
  border: none;
  border-bottom: 2px solid #f0f0f0;
  background: transparent;
  font-size: 16px;
  color: #333;
  transition: all 0.3s;
  outline: none;
}

.form-input:focus {
  border-bottom-color: #ff8a8a;
}

.form-input::placeholder {
  color: #ccc;
}

/* 确定按钮 */
.confirm-btn {
  padding: 8px 20px;
  background: #ff8a8a;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 60px;
}

.confirm-btn:hover {
  background: #ff7575;
}

.confirm-btn:disabled {
  background: #ffb3b3;
  cursor: not-allowed;
}

/* 密码提示 */
.form-tips {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  font-size: 14px;
  color: #666;
  border: 1px solid #f0f0f0;
}

.form-tips p {
  margin: 0 0 10px 0;
  font-weight: 600;
  color: #333;
}

.form-tips ul {
  margin: 0;
  padding-left: 18px;
}

.form-tips li {
  margin: 6px 0;
  line-height: 1.5;
}

@media (max-width: 480px) {
  .form-container {
    padding: 20px 15px;
  }
}
</style>
