{"name": "app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs", "format": "prettier --write \"src/**/*.{js,vue,css}\"", "format:check": "prettier --check \"src/**/*.{js,vue,css}\""}, "dependencies": {"vue": "^3.5.13", "vue-lazyload": "^3.0.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "eslint": "^9.29.0", "eslint-plugin-vue": "^10.2.0", "pinia": "^3.0.3", "postcss-px-to-viewport": "^1.1.1", "vite": "^6.3.5", "vue-router": "^4.5.1"}}