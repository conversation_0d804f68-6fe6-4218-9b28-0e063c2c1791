import { http } from '../utils/request.js'
import { uploadService } from './uploadService.js'

/**
 * 消息相关 API 服务
 */
export const messageService = {
  /**
   * 获取聊天联系人列表
   * @returns {Promise}
   */
  async getContacts() {
    try {
      const response = await http.get('/messages/contacts')
      return response
    } catch (error) {
      throw new Error(error.message || '获取联系人列表失败')
    }
  },

  /**
   * 获取聊天记录
   * @param {number} contactId - 联系人ID
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   * @param {string} params.before - 在此消息ID之前
   * @returns {Promise}
   */
  async getChatHistory(contactId, params = {}) {
    try {
      const response = await http.get(`/messages/chat/${contactId}`, params)
      return response
    } catch (error) {
      throw new Error(error.message || '获取聊天记录失败')
    }
  },

  /**
   * 发送消息
   * @param {Object} messageData - 消息数据
   * @param {number} messageData.receiverId - 接收者ID
   * @param {string} messageData.content - 消息内容
   * @param {string} messageData.type - 消息类型：'text' 或 'image'
   * @returns {Promise}
   */
  async sendMessage(messageData) {
    try {
      const response = await http.post('/messages/send', messageData)
      return response
    } catch (error) {
      throw new Error(error.message || '发送消息失败')
    }
  },

  /**
   * 标记消息已读
   * @param {Object} readData - 已读数据
   * @param {number} readData.contactId - 联系人ID
   * @param {string[]} readData.messageIds - 消息ID数组
   * @returns {Promise}
   */
  async markAsRead(readData) {
    try {
      const response = await http.put('/messages/read', readData)
      return response
    } catch (error) {
      throw new Error(error.message || '标记已读失败')
    }
  },

  /**
   * 上传聊天图片
   * @param {File} imageFile - 图片文件
   * @returns {Promise}
   */
  async uploadChatImage(imageFile) {
    return await uploadService.uploadChatImage(imageFile)
  }
} 