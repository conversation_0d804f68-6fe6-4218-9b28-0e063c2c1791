import { http } from '../utils/request.js'

/**
 * 帖子相关 API 服务
 */
export const postService = {
  /**
   * 获取动态列表
   * @param {Object} params - 查询参数
   * @param {string} params.type - 排序类型：'latest'|'hot'|'follow'
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   * @returns {Promise}
   */
  async getPosts(params = {}) {
    try {
      const response = await http.get('/posts', params)
      return response
    } catch (error) {
      throw new Error(error.message || '获取动态列表失败')
    }
  },

  /**
   * 获取帖子详情
   * @param {number} postId - 帖子ID
   * @returns {Promise}
   */
  async getPostDetail(postId) {
    try {
      const response = await http.get(`/posts/${postId}`)
      return response
    } catch (error) {
      throw new Error(error.message || '获取帖子详情失败')
    }
  },

  /**
   * 创建帖子
   * @param {Object} postData - 帖子数据
   * @param {string} postData.content - 帖子内容
   * @param {string[]} postData.images - 图片URL数组
   * @param {string} postData.location - 位置
   * @param {boolean} postData.isPrivate - 是否私密
   * @returns {Promise}
   */
  async createPost(postData) {
    try {
      const response = await http.post('/posts', postData)
      return response
    } catch (error) {
      throw new Error(error.message || '创建帖子失败')
    }
  },

  /**
   * 删除帖子
   * @param {number} postId - 帖子ID
   * @returns {Promise}
   */
  async deletePost(postId) {
    try {
      const response = await http.delete(`/posts/${postId}`)
      return response
    } catch (error) {
      throw new Error(error.message || '删除帖子失败')
    }
  },

  /**
   * 点赞/取消点赞帖子
   * @param {number} postId - 帖子ID
   * @param {string} action - 操作类型：'like' 或 'unlike'
   * @returns {Promise}
   */
  async likePost(postId, action) {
    try {
      const response = await http.post(`/posts/${postId}/like`, { action })
      return response
    } catch (error) {
      throw new Error(error.message || '点赞操作失败')
    }
  },

  /**
   * 分享帖子
   * @param {number} postId - 帖子ID
   * @returns {Promise}
   */
  async sharePost(postId) {
    try {
      const response = await http.post(`/posts/${postId}/share`)
      return response
    } catch (error) {
      throw new Error(error.message || '分享帖子失败')
    }
  },

  /**
   * 设置帖子隐私
   * @param {number} postId - 帖子ID
   * @param {boolean} isPrivate - 是否私密
   * @returns {Promise}
   */
  async setPostPrivacy(postId, isPrivate) {
    try {
      const response = await http.put(`/posts/${postId}/privacy`, { isPrivate })
      return response
    } catch (error) {
      throw new Error(error.message || '设置隐私失败')
    }
  },

  /**
   * 获取帖子评论
   * @param {number} postId - 帖子ID
   * @param {Object} params - 查询参数
   * @param {string} params.sort - 排序方式：'latest'|'hot'
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   * @returns {Promise}
   */
  async getComments(postId, params = {}) {
    try {
      const response = await http.get(`/posts/${postId}/comments`, params)
      return response
    } catch (error) {
      throw new Error(error.message || '获取评论失败')
    }
  },

  /**
   * 添加评论
   * @param {number} postId - 帖子ID
   * @param {Object} commentData - 评论数据
   * @param {string} commentData.content - 评论内容
   * @param {string} commentData.image - 图片URL（可选）
   * @param {number} commentData.replyTo - 回复的评论ID（可选）
   * @returns {Promise}
   */
  async addComment(postId, commentData) {
    try {
      const response = await http.post(`/posts/${postId}/comments`, commentData)
      return response
    } catch (error) {
      throw new Error(error.message || '添加评论失败')
    }
  },

  /**
   * 删除评论
   * @param {number} commentId - 评论ID
   * @returns {Promise}
   */
  async deleteComment(commentId) {
    try {
      const response = await http.delete(`/posts/comments/${commentId}`)
      return response
    } catch (error) {
      throw new Error(error.message || '删除评论失败')
    }
  },

  /**
   * 点赞/取消点赞评论
   * @param {number} commentId - 评论ID
   * @param {string} action - 操作类型：'like' 或 'unlike'
   * @returns {Promise}
   */
  async likeComment(commentId, action) {
    try {
      const response = await http.post(`/posts/comments/${commentId}/like`, { action })
      return response
    } catch (error) {
      throw new Error(error.message || '点赞评论失败')
    }
  },

  /**
   * 获取用户的帖子列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  async getUserPosts(userId, params = {}) {
    try {
      const response = await http.get(`/user/${userId}/posts`, params)
      return response
    } catch (error) {
      throw new Error(error.message || '获取用户帖子失败')
    }
  },

  /**
   * 切换帖子点赞状态
   * @param {number} postId - 帖子ID
   * @returns {Promise}
   */
  async toggleLike(postId) {
    try {
      const response = await http.post(`/posts/${postId}/toggle-like`)
      return response
    } catch (error) {
      throw new Error(error.message || '点赞操作失败')
    }
  },

  /**
   * 切换帖子隐私状态
   * @param {number} postId - 帖子ID
   * @returns {Promise}
   */
  async togglePrivacy(postId) {
    try {
      const response = await http.put(`/posts/${postId}/toggle-privacy`)
      return response
    } catch (error) {
      throw new Error(error.message || '切换隐私状态失败')
    }
  }
} 