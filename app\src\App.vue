<script>
import { watch, onMounted } from 'vue'
import { useMessageStore } from './stores/messageStore'
import { useUserStore } from './stores/userStore'
import { userService } from './services/userService'
import config from './config'

export default {
  name: 'App',
  setup() {
    const messageStore = useMessageStore()
    const userStore = useUserStore()
    
    // 应用初始化
    onMounted(() => {
      document.title = config.APP_TITLE
    })
    
    // 预加载省市数据
    const preloadRegionData = async () => {
      try {
        await userService.getRegions()
      } catch (error) {
        // 静默处理预加载失败
      }
    }
    
    // 登录初始化
    const handleUserLogin = async () => {
      try {
        // 初始化消息服务
        const userData = {
          id: userStore.userInfo.id,
          name: userStore.userInfo.nickname || userStore.userInfo.username,
          avatar: userStore.userInfo.avatar,
          token: userStore.token
        }
        messageStore.init(userData)
        messageStore.initData()
        
        // 预加载数据
        await preloadRegionData()
      } catch (error) {
        // 静默处理登录初始化失败
      }
    }
    
    // 登出清理
    const handleUserLogout = () => {
      try {
        messageStore.disconnectSocket()
      } catch (error) {
        // 静默处理登出清理失败
      }
    }
    
    // 监听登录状态变化
    watch(() => userStore.isLogin, (isLogin) => {
      isLogin ? handleUserLogin() : handleUserLogout()
    }, { immediate: true })
    
    return {}
  }
}
</script>

<template>
  <router-view v-slot="{ Component }">
    <transition name="fade" mode="out-in">
      <component :is="Component" :key="$route.path" />
    </transition>
  </router-view>
</template>

<style>
#app {
  width: 100%;
  height: 100%;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
