<template>
  <page-layout title="服务条款" :show-back="true" hide-tab-bar hide-safe-area>
    <div class="terms-container">
      <div class="terms-content">
        <div class="section">
          <h3>1. 服务说明</h3>
          <p>本应用是一个社交媒体平台，为用户提供发布内容、互动交流等服务。使用本应用即表示您同意遵守以下条款。</p>
        </div>

        <div class="section">
          <h3>2. 用户账户</h3>
          <p>用户需要创建账户才能使用本应用的完整功能。您有责任保护您的账户信息安全，包括用户名和密码。</p>
        </div>

        <div class="section">
          <h3>3. 内容发布</h3>
          <p>用户发布的内容应当符合法律法规，不得包含违法、有害、虚假信息。我们保留删除违规内容的权利。</p>
        </div>

        <div class="section">
          <h3>4. 用户行为</h3>
          <p>用户应当文明使用本应用，不得进行骚扰、欺凌、垃圾信息发送等不当行为。违规用户将面临账户限制或封禁。</p>
        </div>

        <div class="section">
          <h3>5. 隐私保护</h3>
          <p>我们重视用户隐私，按照隐私政策处理用户信息。详细信息请查看我们的隐私政策。</p>
        </div>

        <div class="section">
          <h3>6. 服务变更</h3>
          <p>我们保留随时修改或终止服务的权利。重要变更将通过应用内通知的方式告知用户。</p>
        </div>

        <div class="section">
          <h3>7. 免责声明</h3>
          <p>本应用按"现状"提供服务，我们不对服务的完整性、准确性、可靠性做出保证。</p>
        </div>

        <div class="section">
          <h3>8. 联系方式</h3>
          <p>如果您对本服务条款有任何疑问，请通过应用内帮助功能联系我们。</p>
        </div>

        <div class="update-time">
          最后更新时间：2024年1月1日
        </div>
      </div>
    </div>
  </page-layout>
</template>

<script setup>
import PageLayout from '../components/PageLayout.vue'
</script>

<style scoped>
.terms-container {
  padding: 15px;
  padding-bottom: 30px;
}

.terms-content {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section {
  margin-bottom: 25px;
}

.section:last-child {
  margin-bottom: 0;
}

.section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  line-height: 1.4;
}

.section p {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.update-time {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
  font-size: 12px;
  color: #999;
  text-align: center;
}
</style> 