import { http, with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../utils/request.js'

/**
 * 认证相关 API 服务
 */
export const authService = {
  /**
   * 用户登录
   * @param {Object} loginData - 登录数据
   * @param {string} loginData.username - 用户名
   * @param {string} loginData.password - 密码
   * @returns {Promise}
   */
  login: with<PERSON>rror<PERSON><PERSON><PERSON>(
    async (loginData) => {
      return await http.post('/auth/login', loginData)
    },
    '登录失败'
  ),

  /**
   * 用户注册
   * @param {Object} registerData - 注册数据
   * @param {string} registerData.username - 用户名
   * @param {string} registerData.password - 密码
   * @param {string} registerData.confirmPassword - 确认密码
   * @returns {Promise}
   */
  register: withError<PERSON>andler(
    async (registerData) => {
      return await http.post('/auth/register', registerData)
    },
    '注册失败'
  ),

  /**
   * 用户登出
   * @returns {Promise}
   */
  logout: with<PERSON><PERSON>r<PERSON><PERSON><PERSON>(
    async () => {
      return await http.post('/auth/logout')
    },
    '登出失败'
  )
} 