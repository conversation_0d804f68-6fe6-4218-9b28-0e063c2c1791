const multer = require('multer')
const path = require('path')
const fs = require('fs-extra')
const { UPLOAD_CONFIG } = require('./constants')

/**
 * 通用的multer存储配置
 */
const createStorage = (destination = 'uploads') => {
  return multer.diskStorage({
    destination: async (req, file, cb) => {
      const uploadPath = path.join(__dirname, '..', destination)
      await fs.ensureDir(uploadPath)
      cb(null, uploadPath)
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
      cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname))
    }
  })
}

/**
 * 文件过滤器
 */
const imageFilter = (req, file, cb) => {
  if (UPLOAD_CONFIG.ALLOWED_IMAGE_TYPES.includes(file.mimetype)) {
    cb(null, true)
  } else {
    cb(new Error('只支持图片文件'))
  }
}

/**
 * 通用上传配置
 */
const uploadConfig = {
  storage: createStorage(),
  limits: {
    fileSize: UPLOAD_CONFIG.MAX_FILE_SIZE
  },
  fileFilter: imageFilter
}

/**
 * 头像上传配置 (更严格的限制)
 */
const avatarUploadConfig = {
  storage: createStorage(),
  limits: {
    fileSize: UPLOAD_CONFIG.MAX_AVATAR_SIZE
  },
  fileFilter: (req, file, cb) => {
    if (UPLOAD_CONFIG.ALLOWED_AVATAR_TYPES.includes(file.mimetype)) {
      cb(null, true)
    } else {
      cb(new Error('头像只支持 JPEG 和 PNG 格式'))
    }
  }
}

/**
 * 聊天图片上传配置
 */
const chatImageUploadConfig = {
  storage: createStorage(),
  limits: {
    fileSize: UPLOAD_CONFIG.MAX_FILE_SIZE
  },
  fileFilter: imageFilter
}

// 创建multer实例
const upload = multer(uploadConfig)
const avatarUpload = multer(avatarUploadConfig)
const chatImageUpload = multer(chatImageUploadConfig)

module.exports = {
  upload,
  avatarUpload,
  chatImageUpload,
  createStorage,
  imageFilter,
  uploadConfig,
  avatarUploadConfig,
  chatImageUploadConfig
} 