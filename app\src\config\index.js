/**
 * 应用配置
 */

// 开发环境配置
const development = {
  API_BASE_URL: 'http://localhost:3000/api',
  WS_URL: 'ws://localhost:3000/ws',
  IMAGE_BASE_URL: 'http://localhost:3000',
  APP_TITLE: '社交匹配应用'
}

// 生产环境配置
const production = {
  API_BASE_URL: '/api',
  WS_URL: `ws://${window.location.host}/ws`,
  IMAGE_BASE_URL: '',
  APP_TITLE: '社交匹配应用'
}

// 测试环境配置
const test = {
  API_BASE_URL: 'http://localhost:3000/api',
  WS_URL: 'ws://localhost:3000/ws',
  IMAGE_BASE_URL: 'http://localhost:3000',
  APP_TITLE: '社交匹配应用(测试)'
}

// 环境配置映射
const configs = {
  development,
  production,
  test
}

// 获取当前环境
const ENV = import.meta.env.MODE || 'development'

// 当前环境配置
const config = {
  ENV,
  ...configs[ENV],
  // 环境变量覆盖
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || configs[ENV].API_BASE_URL,
  WS_URL: import.meta.env.VITE_WS_URL || configs[ENV].WS_URL,
  IMAGE_BASE_URL: import.meta.env.VITE_IMAGE_BASE_URL || configs[ENV].IMAGE_BASE_URL,
  APP_TITLE: import.meta.env.VITE_APP_TITLE || configs[ENV].APP_TITLE,
  // 默认头像路径
  DEFAULT_AVATAR: '/uploads/default-avatar.png',
  // 分页配置
  PAGE_SIZE: 20,
  // 上传配置
  UPLOAD_CONFIG: {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    MAX_AVATAR_SIZE: 5 * 1024 * 1024, // 5MB
    ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
    ALLOWED_AVATAR_TYPES: ['image/jpeg', 'image/jpg', 'image/png'],
    MAX_PHOTOS_COUNT: 6,
    MAX_POST_IMAGES: 9
  }
}

// 在开发环境下可以通过注释启用调试信息
// if (ENV === 'development') {
//   console.log('🔧 应用配置:', config)
// }

export default config

// 导出常用配置
export const { API_BASE_URL, WS_URL, IMAGE_BASE_URL, APP_TITLE, DEFAULT_AVATAR } = config

/**
 * 获取完整的图片URL
 * @param {string} path - 图片路径，如 '/uploads/avatar.png' 或 'avatar.png'
 * @returns {string} 完整的图片URL
 */
export const getImageUrl = (path) => {
  if (!path) return `${config.IMAGE_BASE_URL}${config.DEFAULT_AVATAR}`
  if (path.startsWith('http')) return path
  if (path.startsWith('/uploads/')) {
    // 后端上传的文件，使用API基础URL
    return `${config.API_BASE_URL.replace('/api', '')}${path}`
  }
  return `${config.IMAGE_BASE_URL}${path}`
} 