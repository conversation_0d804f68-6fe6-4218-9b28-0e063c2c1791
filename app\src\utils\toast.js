import { createApp } from 'vue'
import Toast from '../components/Toast.vue'

// 默认配置
const defaultOptions = {
  message: '',
  duration: 3000,
  type: 'default',
  position: 'top',
  showIcon: true,
  closeOnClick: true
}

// Toast实例集合
const instances = []
let seed = 1

// 创建单个toast实例
function createToast(message, options = {}) {
  // 处理参数 - 如果options是字符串，认为是type
  let finalOptions
  if (typeof message === 'object') {
    finalOptions = message
  } else {
    // 如果options是字符串，认为是type参数
    if (typeof options === 'string') {
      finalOptions = {
        message,
        type: options,
        id: `toast_${seed++}`
      }
    } else {
      finalOptions = {
        ...options,
        message,
        id: `toast_${seed++}`
      }
    }
  }

  // 合并选项
  const mergedOptions = {
    ...defaultOptions,
    ...finalOptions
  }

  // 创建挂载节点
  const mountNode = document.createElement('div')
  document.body.appendChild(mountNode)

  // 创建toast实例
  const toastApp = createApp(Toast, {
    ...mergedOptions
  })

  // 挂载实例
  const vm = toastApp.mount(mountNode)

  // 清理函数
  const unmount = () => {
    // 移除事件监听器
    document.removeEventListener('toast:hidden', handleHidden)
    
    if (vm && vm.visible) {
      vm.visible = false
      // 使用nextTick确保动画完成后再卸载
      setTimeout(() => {
        try {
          if (toastApp && typeof toastApp.unmount === 'function') {
            toastApp.unmount()
          }
          if (mountNode && mountNode.parentNode === document.body) {
            document.body.removeChild(mountNode)
          }
        } catch (error) {
          // 静默处理清理错误
        }
        // 从实例集合中移除
        const index = instances.findIndex(item => item.id === mergedOptions.id)
        if (index !== -1) {
          instances.splice(index, 1)
        }
      }, 300) // 等待动画结束
    } else {
      // 如果已经不可见，直接卸载
      try {
        if (toastApp && typeof toastApp.unmount === 'function') {
          toastApp.unmount()
        }
        if (mountNode && mountNode.parentNode === document.body) {
          document.body.removeChild(mountNode)
        }
      } catch (error) {
        // 静默处理清理错误
      }
      // 从实例集合中移除
      const index = instances.findIndex(item => item.id === mergedOptions.id)
      if (index !== -1) {
        instances.splice(index, 1)
      }
    }
  }

  // 监听toast:hidden事件
  const handleHidden = _event => {
    // 延迟一点时间确保是当前toast触发的事件
    setTimeout(() => {
      if (!vm.visible) {
        unmount()
      }
    }, 10)
  }

  // 添加事件监听
  document.addEventListener('toast:hidden', handleHidden)

  // 为组件设置卸载方法
  if (vm.$el) {
    vm.$el._unmount = unmount
  }

  // 当toast隐藏时自动卸载
  if (mergedOptions.duration > 0) {
    setTimeout(() => {
      unmount()
    }, mergedOptions.duration)
  }

  // 添加到实例集合
  const instance = {
    id: mergedOptions.id,
    vm,
    unmount
  }
  instances.push(instance)

  return instance
}

// 导出toast方法
const toast = {
  // 显示Toast
  show(message, options = {}) {
    return createToast(message, options)
  },

  // 成功Toast
  success(message, options = {}) {
    return createToast(message, { ...options, type: 'success' })
  },

  // 错误Toast
  error(message, options = {}) {
    return createToast(message, { ...options, type: 'error' })
  },

  // 警告Toast
  warning(message, options = {}) {
    return createToast(message, { ...options, type: 'warning' })
  },

  // 信息Toast
  info(message, options = {}) {
    return createToast(message, { ...options, type: 'info' })
  },

  // 关闭所有Toast
  closeAll() {
    // 创建实例数组的副本以避免在遍历时修改原数组
    const instancesToClose = [...instances]
    instancesToClose.forEach(instance => {
      try {
        instance.unmount()
      } catch (error) {
        // 静默处理toast实例关闭错误
      }
    })
    // 清空实例数组
    instances.length = 0
    return this
  },

  // 自定义配置
  config(options) {
    Object.assign(defaultOptions, options)
    return this
  },

  // 设置位置
  setPosition(position) {
    defaultOptions.position = position
    return this
  },

  // 设置持续时间
  setDuration(duration) {
    defaultOptions.duration = duration
    return this
  },

  // 设置是否显示图标
  setShowIcon(showIcon) {
    defaultOptions.showIcon = showIcon
    return this
  },

  // 设置是否点击关闭
  setCloseOnClick(closeOnClick) {
    defaultOptions.closeOnClick = closeOnClick
    return this
  },

  // 关闭Toast实例
  close(instance) {
    const index = instances.indexOf(instance)
    if (index > -1) {
      instances.splice(index, 1)
    }
  }
}

export default toast
