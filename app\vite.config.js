import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  
  // 定义全局常量
  define: {
    __API_BASE_URL__: JSON.stringify(process.env.VITE_API_BASE_URL || 'http://localhost:3000/api'),
    __WS_URL__: JSON.stringify(process.env.VITE_WS_URL || 'ws://localhost:3000/ws')
  },
  
  // 开发服务器配置
  server: {
    port: 5173,
    host: true,
    // 代理配置（可选，如果需要避免CORS问题）
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false
      },
      '/uploads': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false
      }
    }
  }
})
