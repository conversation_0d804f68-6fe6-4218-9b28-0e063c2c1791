<template>
  <div class="empty-state">
    <div class="empty-icon" v-if="icon">{{ icon }}</div>
    <div class="empty-text">{{ text }}</div>
    <div class="empty-desc" v-if="description">{{ description }}</div>
  </div>
</template>

<script setup>
defineProps({
  icon: {
    type: String,
    default: ''
  },
  text: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  }
})
</script>
