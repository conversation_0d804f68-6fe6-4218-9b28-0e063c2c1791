<template>
  <div 
    class="action-button" 
    :class="[type, { disabled, loading }]" 
    role="button"
    :tabindex="disabled ? -1 : 0"
    :aria-label="ariaLabel || text"
    :aria-disabled="disabled"
    @click="handleClick"
    @keydown.enter="handleClick"
    @keydown.space.prevent="handleClick"
  >
    <div v-if="loading" class="loading-spinner"></div>
    <i v-else-if="icon" class="action-icon">{{ icon }}</i>
    <span v-if="text && !loading" class="action-text">{{ text }}</span>
  </div>
</template>

<script setup>
/**
 * ActionButton 通用按钮组件
 * 支持多种样式类型：default、like、dislike、primary、secondary
 * 支持图标、文本、禁用状态和加载状态
 */

const props = defineProps({
  /** 按钮类型 */
  type: {
    type: String,
    default: 'default',
    validator: value => ['default', 'like', 'dislike', 'primary', 'secondary'].includes(value)
  },
  /** 图标内容 */
  icon: {
    type: String,
    default: ''
  },
  /** 按钮文本 */
  text: {
    type: String,
    default: ''
  },
  /** 是否禁用 */
  disabled: {
    type: Boolean,
    default: false
  },
  /** 是否加载中 */
  loading: {
    type: Boolean,
    default: false
  },
  /** 无障碍标签 */
  ariaLabel: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['click'])

const handleClick = () => {
  if (!props.disabled && !props.loading) {
    emit('click')
  }
}
</script>

<style scoped>
.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  transition: all var(--transition-fast) ease;
  position: relative;
  outline: none;
}

.action-button:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.action-button:active:not(.disabled):not(.loading) {
  transform: scale(0.95);
}

.action-button.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.action-button.loading {
  cursor: default;
}

/* 加载动画 */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 圆形按钮 (like, dislike) */
.like,
.dislike {
  width: 65px;
  height: 65px;
  border-radius: 50%;
}

.like {
  background-color: var(--color-primary);
  box-shadow: 0 4px 12px rgba(255, 88, 100, 0.2);
}

.like .action-icon {
  font-size: var(--font-size-xxl);
  color: #fff;
}

.like.disabled {
  background-color: var(--color-disabled, #ccc);
  box-shadow: none;
}

.dislike {
  background-color: #fff;
  border: 1px solid var(--color-border);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.dislike .action-icon {
  font-size: var(--font-size-xxl);
  color: var(--font-color-tertiary);
}

.dislike.disabled {
  background-color: var(--color-bg-disabled, #f5f5f5);
  border-color: var(--color-border-disabled, #e0e0e0);
}

/* 默认按钮样式 */
.default {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-sm);
  min-height: 60px;
}

.default .action-icon {
  font-size: var(--font-size-xxl);
  margin-bottom: var(--spacing-xs);
  color: var(--font-color-secondary);
}

.default .action-text {
  font-size: var(--font-size-sm);
  color: var(--font-color-tertiary);
}

.default.disabled .action-icon,
.default.disabled .action-text {
  color: var(--font-color-disabled, #ccc);
}

/* 主要和次要按钮 */
.primary,
.secondary {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 20px;
  font-size: var(--font-size-md);
  min-height: 44px;
  font-weight: 500;
}

.primary {
  background-color: var(--color-primary);
  color: #fff;
  border: none;
}

.primary:hover:not(.disabled):not(.loading) {
  background-color: var(--color-primary-hover, var(--color-primary));
  transform: translateY(-1px);
}

.primary.disabled {
  background-color: var(--color-disabled, #ccc);
}

.secondary {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.secondary:hover:not(.disabled):not(.loading) {
  background-color: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.secondary.disabled {
  background-color: var(--color-bg-disabled, #f5f5f5);
  color: var(--font-color-disabled, #ccc);
  border-color: var(--color-border-disabled, #e0e0e0);
}

/* 加载状态的特定样式 */
.like.loading .loading-spinner,
.dislike.loading .loading-spinner {
  color: currentColor;
}

.primary.loading .loading-spinner {
  color: #fff;
}

.secondary.loading .loading-spinner {
  color: currentColor;
}
</style>
