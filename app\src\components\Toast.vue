<template>
  <transition name="toast-fade">
    <div v-if="visible" class="toast-container" :class="[`toast-${type}`, `toast-${position}`]">
      <div class="toast-inner">
        <div v-if="showIcon" class="toast-icon">
          <svg v-if="type === 'success'" viewBox="0 0 24 24" class="icon">
            <path
              d="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M10,17l-5-5l1.41-1.41L10,14.17l7.59-7.59L19,8L10,17z" />
          </svg>
          <svg v-else-if="type === 'error'" viewBox="0 0 24 24" class="icon">
            <path
              d="M12,2C6.47,2,2,6.47,2,12s4.47,10,10,10s10-4.47,10-10S17.53,2,12,2z M17,15.59L15.59,17L12,13.41L8.41,17L7,15.59L10.59,12L7,8.41L8.41,7L12,10.59L15.59,7L17,8.41L13.41,12L17,15.59z" />
          </svg>
          <svg v-else-if="type === 'warning'" viewBox="0 0 24 24" class="icon">
            <path
              d="M12,2L1,21h22L12,2z M12,18c-0.55,0-1-0.45-1-1s0.45-1,1-1s1,0.45,1,1S12.55,18,12,18z M11,15V9h2v6H11z" />
          </svg>
          <svg v-else-if="type === 'info'" viewBox="0 0 24 24" class="icon">
            <path
              d="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M13,17h-2v-6h2V17z M13,9h-2V7h2V9z" />
          </svg>
        </div>
        <div class="toast-content">{{ message }}</div>
      </div>
    </div>
  </transition>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'

const props = defineProps({
  message: {
    type: String,
    default: ''
  },
  duration: {
    type: Number,
    default: 3000
  },
  type: {
    type: String,
    default: 'default',
    validator: value => ['success', 'error', 'warning', 'info', 'default'].includes(value)
  },
  position: {
    type: String,
    default: 'top',
    validator: value => ['top', 'center', 'bottom'].includes(value)
  },
  showIcon: {
    type: Boolean,
    default: true
  },
  closeOnClick: {
    type: Boolean,
    default: true
  }
})

const visible = ref(false)
let timer = null

const close = () => {
  visible.value = false
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
}

// 点击文档关闭Toast
const handleDocumentClick = () => {
  if (props.closeOnClick && visible.value) {
    close()
  }
}

// 监听visible变化，确保在隐藏后通知父组件
watch(visible, newVal => {
  if (!newVal) {
    // 触发自定义事件，通知父组件组件已隐藏
    const event = new CustomEvent('toast:hidden')
    document.dispatchEvent(event)
  }
})

onMounted(() => {
  // 使用nextTick确保DOM已更新
  setTimeout(() => {
    visible.value = true
    if (props.duration > 0) {
      timer = setTimeout(() => {
        close()
      }, props.duration)
    }

    // 添加点击事件监听
    if (props.closeOnClick) {
      document.addEventListener('click', handleDocumentClick)
    }
  }, 0)
})

onBeforeUnmount(() => {
  if (timer) {
    clearTimeout(timer)
  }
  // 移除点击事件监听
  document.removeEventListener('click', handleDocumentClick)
})

// 暴露方法给父组件
defineExpose({
  visible,
  close
})
</script>

<style scoped>
.toast-container {
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 14px 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-card);
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  font-size: var(--font-size-lg);
  font-weight: 500;
  line-height: 1.5;
  z-index: var(--z-index-toast);
  left: 50%;
  max-width: 85%;
  min-width: 200px;
  pointer-events: auto;
  cursor: pointer;
  backdrop-filter: blur(4px);
  transform-origin: center;
}

.toast-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.toast-content {
  word-break: break-word;
  text-align: center;
  width: 100%;
}

.toast-top {
  top: var(--spacing-xl);
  transform: translateX(-50%);
}

.toast-center {
  top: 50%;
  transform: translate(-50%, -50%);
}

.toast-bottom {
  bottom: var(--spacing-xl);
  transform: translateX(-50%);
}

.toast-icon {
  margin-right: var(--spacing-md);
  display: flex;
  align-items: center;
}

.icon {
  width: var(--font-size-xl);
  height: var(--font-size-xl);
  fill: currentColor;
}

.toast-success {
  background-color: rgba(82, 196, 26, 0.9);
  border-left: 4px solid var(--color-success);
}

.toast-error {
  background-color: rgba(245, 34, 45, 0.9);
  border-left: 4px solid var(--color-error);
}

.toast-warning {
  background-color: rgba(250, 173, 20, 0.9);
  border-left: 4px solid var(--color-warning);
}

.toast-info {
  background-color: rgba(24, 144, 255, 0.9);
  border-left: 4px solid var(--color-info);
}

.toast-default {
  background-color: rgba(0, 0, 0, 0.8);
  border-left: 4px solid rgba(255, 255, 255, 0.8);
}

.toast-fade-enter-active,
.toast-fade-leave-active {
  transition:
    opacity var(--transition-normal),
    transform var(--transition-normal) cubic-bezier(0.23, 1, 0.32, 1);
}

.toast-fade-enter-from,
.toast-fade-leave-to {
  opacity: 0;
}

.toast-top.toast-fade-enter-from,
.toast-top.toast-fade-leave-to {
  transform: translateX(-50%) translateY(calc(-1 * var(--spacing-xl)));
}

.toast-center.toast-fade-enter-from,
.toast-center.toast-fade-leave-to {
  transform: translate(-50%, -40%) scale(0.9);
}

.toast-bottom.toast-fade-enter-from,
.toast-bottom.toast-fade-leave-to {
  transform: translateX(-50%) translateY(var(--spacing-xl));
}
</style>
