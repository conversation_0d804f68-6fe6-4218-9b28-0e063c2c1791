<template>
  <div class="user-card" :class="[
    `user-card-${type}`,
    {
      'user-card-blur': blur,
      'contact-unread': type === 'contact' && unreadCount > 0
    }
  ]" @click="$emit('click')">
    <!-- 模糊遮罩层 -->
    <div v-if="blur" class="blur-overlay">
      <div class="unlock-hint">{{ blurText || '升级会员解锁' }}</div>
    </div>

    <!-- 网格卡片 -->
    <template v-if="type === 'grid'">
      <div class="user-card-image">
        <div class="avatar-container">
          <avatar :src="avatar" :alt="username" size="large" />
        </div>
        <div v-if="matched" class="match-status">匹配</div>
      </div>
      <div class="user-card-info">
        <div class="user-name">{{ username }}</div>
        <div class="user-age">{{ age }}岁</div>
      </div>
    </template>

    <!-- 联系人卡片 -->
    <template v-else-if="type === 'contact'">
      <div class="contact-avatar">
        <avatar :src="avatar" :alt="username" size="medium" :badge="unreadCount > 0 ? unreadCount : ''"
          badge-type="number" />
      </div>
      <div class="contact-info">
        <div class="contact-row">
          <span class="contact-name">{{ username }}</span>
          <span v-if="lastMessageTime" class="contact-time">{{ lastMessageTime }}</span>
        </div>
        <p v-if="lastMessage" class="contact-message">{{ lastMessage }}</p>
      </div>
    </template>

    <!-- 匹配卡片 -->
    <template v-else-if="type === 'match'">
      <div class="match-avatar">
        <avatar :src="avatar" :alt="username" size="medium" border :badge="isNew ? 'dot' : ''" badge-type="dot" />
      </div>
      <span class="match-name">{{ username }}</span>
    </template>
  </div>
</template>

<script setup>
import Avatar from './Avatar.vue'

const emit = defineEmits(['click'])

defineProps({
  type: {
    type: String,
    default: 'grid', // grid, contact, match
    validator: value => ['grid', 'contact', 'match'].includes(value)
  },
  username: {
    type: String,
    required: true
  },
  age: {
    type: [Number, String],
    default: ''
  },
  avatar: {
    type: String,
    default: ''
  },
  matched: {
    type: Boolean,
    default: false
  },
  blur: {
    type: Boolean,
    default: false
  },
  blurText: {
    type: String,
    default: ''
  },
  lastMessage: {
    type: String,
    default: ''
  },
  lastMessageTime: {
    type: String,
    default: ''
  },
  unreadCount: {
    type: [Number, String],
    default: 0
  },
  isNew: {
    type: Boolean,
    default: false
  }
})
</script>

<style scoped>
/* 基础样式 */
.user-card {
  position: relative;
}

/* 模糊遮罩 */
.user-card-blur {
  position: relative;
}

.blur-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

.unlock-hint {
  color: #fff;
  font-size: var(--font-size-md);
  background-color: rgba(255, 107, 107, 0.8);
  padding: 5px 10px;
  border-radius: 20px;
}

/* 网格卡片样式 */
.user-card-grid {
  border-radius: var(--border-radius);
  background-color: #fff;
  box-shadow: var(--shadow-card);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.user-card-grid .user-card-image {
  position: relative;
  width: 100%;
  flex: 1;
  min-height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  overflow: hidden;
}

.avatar-container {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
  margin: 0 auto;
}

.avatar-container :deep(.avatar-wrapper) {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-container :deep(.avatar) {
  width: 100%;
  height: 100%;
}

.avatar-container :deep(.avatar img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.match-status {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--color-primary);
  color: #fff;
  font-size: var(--font-size-sm);
  padding: 3px var(--spacing-sm);
  border-radius: 10px;
  z-index: 1;
}

.user-card-grid .user-card-info {
  padding: var(--spacing-sm);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
}

.user-card-grid .user-name {
  font-size: var(--font-size-lg);
  font-weight: 500;
}

.user-card-grid .user-age {
  font-size: var(--font-size-md);
  color: var(--font-color-tertiary);
}

/* 联系人卡片样式 */
.user-card-contact {
  display: flex;
  padding: var(--spacing-md) var(--spacing-md);
  border-bottom: 1px solid var(--color-background);
  width: 100%;
  background-color: #fff;
  transition: background-color var(--transition-fast);
}

.user-card-contact:active {
  background-color: var(--color-background);
}

.user-card-contact.contact-unread {
  background-color: #fff;
  position: relative;
}

.user-card-contact.contact-unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 60%;
  background-color: var(--color-primary);
  border-radius: 0 3px 3px 0;
}

.user-card-contact:last-child {
  border-bottom: none;
}

.contact-avatar {
  margin-right: var(--spacing-md);
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
  width: 50px;
  height: 50px;
}

.contact-avatar :deep(.avatar-wrapper) {
  width: 100%;
  height: 100%;
}

.contact-avatar :deep(.avatar) {
  width: 100%;
  height: 100%;
}

.contact-info {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.contact-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.contact-name {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--font-color-primary);
}

.contact-time {
  font-size: var(--font-size-sm);
  color: var(--font-color-light);
}

.contact-message {
  font-size: var(--font-size-sm);
  color: var(--font-color-tertiary);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

/* 匹配卡片样式 */
.user-card-match {
  flex: 0 0 auto;
  width: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.match-avatar {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-xs);
  position: relative;
  z-index: 1;
  width: 50px;
  height: 50px;
}

.match-avatar :deep(.avatar-wrapper) {
  width: 100%;
  height: 100%;
}

.match-avatar :deep(.avatar) {
  width: 100%;
  height: 100%;
}

.match-name {
  font-size: var(--font-size-sm);
  color: var(--font-color-primary);
  text-align: center;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
