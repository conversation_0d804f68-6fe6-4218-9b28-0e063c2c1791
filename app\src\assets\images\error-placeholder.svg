<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
  <defs>
    <linearGradient id="errorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ff8a80" />
      <stop offset="100%" stop-color="#ff5252" />
    </linearGradient>
  </defs>
  
  <rect width="200" height="200" fill="#fff" />
  <circle cx="100" cy="100" r="70" fill="url(#errorGradient)" opacity="0.9">
    <animate attributeName="opacity" values="0.8;0.9;0.8" dur="3s" repeatCount="indefinite" />
  </circle>
  
  <g stroke="#ffffff" stroke-width="12" stroke-linecap="round">
    <line x1="70" y1="70" x2="130" y2="130">
      <animate attributeName="stroke-width" values="12;14;12" dur="3s" repeatCount="indefinite" />
    </line>
    <line x1="130" y1="70" x2="70" y2="130">
      <animate attributeName="stroke-width" values="12;14;12" dur="3s" repeatCount="indefinite" />
    </line>
  </g>
</svg> 