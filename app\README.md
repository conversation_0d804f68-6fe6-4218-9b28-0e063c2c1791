# Vue 3 + Vite

This template should help get you started developing with Vue 3 in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

Learn more about IDE Support for Vue in the [Vue Docs Scaling up Guide](https://vuejs.org/guide/scaling-up/tooling.html#ide-support).

# 社交匹配应用

这是一个基于Vue 3 + Vite的社交匹配应用，类似于交友软件，包含用户认证、个人资料、匹配系统、聊天消息、帖子动态等功能。

## 技术栈

- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **HTTP客户端**: 原生 Fetch API
- **WebSocket**: 原生 WebSocket
- **样式**: CSS3 + PostCSS

## 项目结构

```
app/
├── src/
│   ├── components/        # 可复用组件
│   ├── views/            # 页面组件
│   ├── services/         # API服务层
│   │   ├── authService.js      # 认证相关API
│   │   ├── userService.js      # 用户相关API
│   │   ├── matchService.js     # 匹配系统API
│   │   ├── messageService.js   # 消息相关API
│   │   ├── postService.js      # 帖子相关API
│   │   └── uploadService.js    # 文件上传API
│   ├── stores/           # 状态管理
│   ├── utils/            # 工具函数
│   │   └── request.js    # HTTP请求封装
│   └── router/           # 路由配置
├── public/               # 静态资源
└── package.json
```

## API服务层说明

项目已完成从模拟数据到真实API的替换，主要包含以下服务：

### 1. 认证服务 (authService.js)
- `login(loginData)` - 用户登录
- `register(registerData)` - 用户注册
- `logout()` - 用户登出

### 2. 用户服务 (userService.js)
- `getProfile()` - 获取用户资料
- `updateProfile(profileData)` - 更新用户资料
- `uploadAvatar(avatarFile)` - 上传头像
- `uploadPhotos(photoFiles)` - 上传相册
- `setAvatar(photoUrl)` - 设置头像

### 3. 匹配服务 (matchService.js)
- `getRecommendations(filters)` - 获取推荐用户列表
- `performAction(targetUserId, action)` - 执行匹配操作
- `getMatches()` - 获取匹配列表
- `getReceivedLikes()` - 获取喜欢我的用户
- `getSentLikes()` - 获取我喜欢的用户

### 4. 消息服务 (messageService.js)
- `getContacts()` - 获取联系人列表
- `getChatHistory(contactId, params)` - 获取聊天记录
- `sendMessage(messageData)` - 发送消息
- `markAsRead(readData)` - 标记消息已读
- `uploadChatImage(imageFile)` - 上传聊天图片

### 5. 帖子服务 (postService.js)
- `getPosts(params)` - 获取动态列表
- `getPostDetail(postId)` - 获取帖子详情
- `createPost(postData)` - 创建帖子
- `deletePost(postId)` - 删除帖子
- `likePost(postId, action)` - 点赞/取消点赞
- `sharePost(postId)` - 分享帖子
- `setPostPrivacy(postId, isPrivate)` - 设置隐私
- 评论相关操作

### 6. 上传服务 (uploadService.js)
- `uploadFile(file, type)` - 通用文件上传
- `uploadFiles(files, type)` - 批量文件上传
- `validateFile(file, options)` - 文件验证

## 环境配置

创建 `.env.development` 文件：

```bash
# 开发环境配置
VITE_API_BASE_URL=http://localhost:3000/api
VITE_WS_URL=ws://localhost:8080/ws
VITE_APP_TITLE=社交匹配应用
```

创建 `.env.production` 文件：

```bash
# 生产环境配置
VITE_API_BASE_URL=https://api.yourdomain.com/api
VITE_WS_URL=wss://api.yourdomain.com/ws
VITE_APP_TITLE=社交匹配应用
```

## 已替换的模拟数据

以下页面和功能已从模拟数据替换为真实API调用：

### 基础页面
- ✅ **Login.vue** - 使用 `authService.login()`
- ✅ **Register.vue** - 使用 `authService.register()`
- ✅ **Home.vue** - 使用 `matchService.getRecommendations()`
- ✅ **Square.vue** - 使用 `postService.getPosts()`

### 状态管理
- ✅ **messageStore.js** - 移除 `loadMockData()`，使用真实API
- ✅ **App.vue** - 更新初始化逻辑

### 功能特性
- ✅ 用户推荐和匹配操作
- ✅ 帖子列表、点赞、分享、删除
- ✅ 联系人和聊天记录加载
- ✅ 文件上传功能
- ✅ 错误处理和用户提示

## 开发指南

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 构建生产版本
```bash
npm run build
```

## API文档

详细的API文档请参考 `api.md` 文件，包含所有接口的请求格式、响应格式和错误码说明。

## 注意事项

1. 所有需要认证的API都需要在请求头中包含 `Authorization: Bearer {token}`
2. WebSocket连接需要携带有效的token参数
3. 文件上传支持的格式：图片(jpg, png, gif)，最大10MB
4. 项目已完成模拟数据清理，所有数据都从真实API获取
5. 错误处理统一通过toast提示用户

## 下一步计划

- 完善其他页面的API集成（如个人资料页、设置页等）
- 添加更多的错误处理和用户体验优化
- 实现实时消息推送和在线状态同步
- 添加单元测试和集成测试

