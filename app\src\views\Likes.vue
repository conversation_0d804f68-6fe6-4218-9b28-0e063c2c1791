<template>
  <page-layout title="喜欢">
    <!-- 顶部标签栏 -->
    <div class="tab-container">
      <tab-header v-model="currentTab" :tabs="tabs" />
    </div>

    <!-- 喜欢我的用户列表 -->
    <div v-if="currentTab === 0" class="user-grid">
      <empty-state
        v-if="likedMeUsers.length === 0"
        icon="💔"
        text="暂时没有人喜欢你"
        description="继续浏览，增加被发现的机会"
      />

      <div v-else class="user-grid-content">
        <div v-for="(user, index) in likedMeUsers" :key="index" class="user-card-wrapper">
          <user-card
            type="grid"
            :username="user.name"
            :age="user.age"
            :avatar="user.avatar"
            :blur="!user.unlocked"
            blur-text="升级会员解锁"
            @click="viewProfile(user.id)"
          />
        </div>
      </div>
    </div>

    <!-- 我喜欢的用户列表 -->
    <div v-if="currentTab === 1" class="user-grid">
      <empty-state
        v-if="myLikesUsers.length === 0"
        icon="❤️"
        text="你还没有喜欢任何人"
        description="去首页发现心动的Ta吧"
      />

      <div v-else class="user-grid-content">
        <div v-for="(user, index) in myLikesUsers" :key="index" class="user-card-wrapper">
          <user-card
            type="grid"
            :username="user.name"
            :age="user.age"
            :avatar="user.avatar"
            :matched="user.matched"
            @click="viewProfile(user.id)"
          />
        </div>
      </div>
    </div>
  </page-layout>
</template>

<script setup>
import { ref, computed, onMounted, inject } from 'vue'
import { useRouter } from 'vue-router'
import PageLayout from '../components/PageLayout.vue'
import TabHeader from '../components/TabHeader.vue'
import EmptyState from '../components/EmptyState.vue'
import UserCard from '../components/UserCard.vue'
import { matchService } from '../services/matchService'
import toast from '../utils/toast.js'

const router = useRouter()
const customToast = inject('toast')

// 当前标签
const currentTab = ref(0)

// 数据状态
const loading = ref(true)
const likedMeUsers = ref([])
const myLikesUsers = ref([])

// 动态标签列表
const tabs = computed(() => [
  { name: '喜欢我的', count: likedMeUsers.value.length },
  { name: '我喜欢的', count: myLikesUsers.value.length }
])

// 加载喜欢我的用户列表
const loadLikedMeUsers = async () => {
  try {
    const response = await matchService.getReceivedLikes()
    likedMeUsers.value = response.map(user => ({
      ...user,
      name: user.nickname || user.username,
      unlocked: !user.isMatched // 示例：未匹配的用户需要会员解锁
    })) || []
  } catch (error) {
    toast.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 加载我喜欢的用户列表
const loadMyLikesUsers = async () => {
  try {
    const response = await matchService.getSentLikes()
    myLikesUsers.value = response.map(user => ({
      ...user,
      name: user.nickname || user.username,
      matched: user.isMatched
    })) || []
  } catch (error) {
    toast.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 初始化数据
const initData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadLikedMeUsers(),
      loadMyLikesUsers()
    ])
  } finally {
    loading.value = false
  }
}

// 查看用户资料
const viewProfile = id => {
  // 查找当前用户是否已解锁
  if (currentTab.value === 0) {
    const user = likedMeUsers.value.find(u => u.id === id)
    if (user && !user.unlocked) {
      // 未解锁用户，显示升级会员提示
      toast.warning('需要升级会员才能查看该用户资料', {
        position: 'center',
        duration: 2000
      })
      return
    }
  }

  // 已解锁用户或我喜欢的用户，正常跳转
  router.push(`/profile/${id}`)
}

// 页面挂载时加载数据
onMounted(() => {
  initData()
})
</script>

<style scoped>
  /* Likes页面特定样式 */
  .tab-container {
    position: relative;
    z-index: 20;
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .user-grid {
    width: 100%;
    position: relative;
    z-index: 1;
  }

  .user-grid-content {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 5px 0;
  }

  .user-card-wrapper {
    height: 100%;
    min-height: 240px;
  }

  @media (max-width: 375px) {
    .user-grid-content {
      gap: 10px;
    }

    .user-card-wrapper {
      min-height: 220px;
    }
  }

  @media (min-width: 768px) {
    .user-grid-content {
      grid-template-columns: repeat(3, 1fr);
    }

    .user-card-wrapper {
      min-height: 260px;
    }
  }
</style>
