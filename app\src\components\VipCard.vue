<template>
  <div class="vip-card">
    <div class="vip-info">
      <div class="vip-title">{{ title }}</div>
      <div class="vip-desc">{{ description }}</div>
    </div>
    <div class="vip-button" @click="$emit('click')">{{ buttonText }}</div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: '开通会员'
  },
  description: {
    type: String,
    default: '解锁更多喜欢，查看谁喜欢你'
  },
  buttonText: {
    type: String,
    default: '立即开通'
  }
})

defineEmits(['click'])
</script>

<style scoped>
.vip-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(45deg, #ff5864, #ff8a98);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  color: #fff;
  box-shadow: 0 2px 8px rgba(255, 88, 100, 0.3);
}

.vip-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 5px;
}

.vip-desc {
  font-size: 12px;
  opacity: 0.8;
}

.vip-button {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
}

.vip-button:active {
  opacity: 0.8;
}
</style>
