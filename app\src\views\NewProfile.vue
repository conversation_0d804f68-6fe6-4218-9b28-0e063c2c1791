<template>
  <div class="profile-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-heart heart-1">💕</div>
      <div class="floating-heart heart-2">💖</div>
      <div class="floating-heart heart-3">✨</div>
    </div>

    <div class="profile-card">
      <div class="profile-header">
        <div class="logo-container">
          <div class="logo">
            <svg
              width="40"
              height="40"
              viewBox="0 0 24 24"
              fill="none"
              stroke="url(#gradient)"
              stroke-width="2.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#ff8e8e;stop-opacity:1" />
                </linearGradient>
              </defs>
              <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
            </svg>
          </div>
        </div>
        <h2>完善资料</h2>
        <p>{{ stepTitles[currentStep] }}</p>
      </div>

      <!-- 步骤进度条 -->
      <div class="progress-container">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${((currentStep + 1) / totalSteps) * 100}%` }"
          ></div>
        </div>
        <span class="progress-text">{{ currentStep + 1 }}/{{ totalSteps }}</span>
      </div>

      <div class="form-container">
        <!-- 步骤1：基本信息 -->
        <div v-if="currentStep === 0" class="step-content">
          <div class="welcome-text">
            <h3>👋 欢迎加入我们</h3>
            <p>让我们先了解一下你</p>
          </div>
          
          <div class="form-group">
            <label>你的昵称</label>
            <div class="input-container">
              <input 
                v-model="form.nickname" 
                type="text" 
                placeholder="输入一个有趣的昵称" 
                maxlength="20"
                required
              />
              <div class="input-icon">😊</div>
            </div>
          </div>
          
          <div class="form-group">
            <label>你的性别</label>
            <div class="gender-selection">
              <div
                class="gender-card"
                :class="{ selected: form.gender === 'male' }"
                @click="form.gender = 'male'"
              >
                <div class="gender-emoji">🙋‍♂️</div>
                <span>我是男生</span>
                <div class="selection-indicator"></div>
              </div>
              <div
                class="gender-card"
                :class="{ selected: form.gender === 'female' }"
                @click="form.gender = 'female'"
              >
                <div class="gender-emoji">🙋‍♀️</div>
                <span>我是女生</span>
                <div class="selection-indicator"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤2：生日 -->
        <div v-if="currentStep === 1" class="step-content">
          <div class="step-icon">🎂</div>
          <div class="form-group">
            <label>你的生日</label>
            <p class="step-description">我们需要确认你的年龄</p>
            <div class="date-picker">
              <div class="date-item">
                <label>年</label>
                <select v-model="form.year" class="date-select">
                  <option v-for="year in years" :key="year" :value="year">{{ year }}</option>
                </select>
              </div>
              <div class="date-item">
                <label>月</label>
                <select v-model="form.month" class="date-select">
                  <option v-for="month in 12" :key="month" :value="month">{{ month }}月</option>
                </select>
              </div>
              <div class="date-item">
                <label>日</label>
                <select v-model="form.day" class="date-select">
                  <option v-for="day in getDaysInMonth(form.year, form.month)" :key="day" :value="day">{{ day }}日</option>
                </select>
              </div>
            </div>
            <div class="age-display">
              你今年 <span class="age-number">{{ calculateAge() }}</span> 岁
            </div>
          </div>
        </div>

        <!-- 步骤3：地区选择 -->
        <div v-if="currentStep === 2" class="step-content">
          <div class="step-icon">📍</div>
          <div class="form-group">
            <label>你的所在地区</label>
            <p class="step-description">帮助我们为你推荐附近的人</p>
            <div class="region-container">
          <RegionSelector
            v-model:province-code="form.provinceCode"
            v-model:city-code="form.cityCode"
            @change="handleRegionChange"
          />
              <div class="region-tips">
                <p>选择你当前所在的省市</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤4：头像 -->
        <div v-if="currentStep === 3" class="step-content">
          <div class="step-icon">📸</div>
          <div class="form-group">
            <label>选择头像</label>
            <p class="step-description">展示你最美的一面</p>
            <div class="avatar-upload-area">
              <div 
                class="avatar-preview-large" 
                @click="triggerFileInput"
                @mouseenter="showOverlay = true"
                @mouseleave="showOverlay = false"
              >
                <img 
                  :src="previewUrl || form.avatarUrl || defaultAvatar" 
                  alt="头像预览"
                  class="avatar-img"
                />
                <div class="upload-overlay" :class="{ visible: showOverlay }">
                  <div class="upload-icon">📷</div>
                  <span>点击上传</span>
                </div>
              </div>
              <input
                ref="fileInput"
                type="file"
                accept="image/*"
                style="display: none"
                @change="handleFileChange"
              />
            </div>
            <div class="upload-tips">
              <p>• 支持 JPG、PNG 格式</p>
              <p>• 文件大小不超过 2MB</p>
            </div>
          </div>
        </div>

        <!-- 步骤5：兴趣爱好 -->
        <div v-if="currentStep === 4" class="step-content">
          <div class="step-icon">❤️</div>
          <div class="form-group">
            <label>你的兴趣</label>
            <p class="step-description">选择你喜欢的事物，最多5个</p>
            <div class="interests-container">
              <div
                v-for="interest in interestOptions"
                :key="interest.id"
                class="interest-bubble"
                :class="{ 
                  selected: selectedInterests.includes(interest.id),
                  disabled: !selectedInterests.includes(interest.id) && selectedInterests.length >= 5
                }"
                @click="toggleInterest(interest.id)"
              >
                <span class="interest-emoji">{{ interest.emoji }}</span>
                <span class="interest-name">{{ interest.name }}</span>
              </div>
            </div>
            <div class="selection-counter">
              已选择 <span class="counter-number">{{ selectedInterests.length }}</span>/5
            </div>
          </div>
        </div>

        <!-- 步骤6：个人简介 -->
        <div v-if="currentStep === 5" class="step-content">
          <div class="step-icon">✍️</div>
          <div class="form-group">
            <label>介绍自己</label>
            <p class="step-description">用一段话展示你的魅力</p>
            <div class="bio-container">
              <textarea 
                v-model="form.bio" 
                placeholder="写下你想对世界说的话..." 
                rows="4"
                maxlength="150"
                class="bio-textarea"
              ></textarea>
              <div class="char-counter">
                <span :class="{ warning: form.bio?.length > 120 }">
                  {{ form.bio?.length || 0 }}
                </span>/150
              </div>
            </div>
            <div class="bio-tips">
              💡 可以写写你的爱好、职业或有趣的经历
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <button 
          v-if="currentStep > 0"
          type="button" 
          @click="prevStep" 
          class="btn btn-secondary"
        >
          ← 上一步
        </button>
        
        <button 
          v-if="currentStep < totalSteps - 1"
          type="button" 
          @click="nextStep" 
          class="btn btn-primary"
          :disabled="!canProceed"
        >
          下一步 →
        </button>
        
        <button 
          v-else
          type="button" 
          @click="handleComplete" 
          class="btn btn-primary btn-complete"
          :disabled="!isFormValid"
        >
          ✨ 开始探索
        </button>
      </div>

      <div class="skip-section">
        <button @click="handleSkip" class="skip-link">
          暂时跳过，稍后完善
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, inject } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/userStore'
import { getImageUrl } from '../config'
import RegionSelector from '../components/RegionSelector.vue'

const router = useRouter()
const userStore = useUserStore()
const toast = inject('toast')

const defaultAvatar = getImageUrl('/default.png')
const fileInput = ref(null)
const previewUrl = ref(null)
const currentStep = ref(0)
const totalSteps = 6
const showOverlay = ref(false)

const stepTitles = [
  '让我们开始认识你',
  '告诉我们你的年龄',
  '选择你的地区',
  '展示你的魅力',
  '分享你的兴趣',
  '说说你的故事'
]

const form = reactive({
  nickname: userStore.userInfo.nickname || userStore.userInfo.username || '',
  gender: '',
  year: new Date().getFullYear() - 22,
  month: 1,
  day: 1,
  bio: '',
  provinceCode: '',
  cityCode: '',
  avatarUrl: getImageUrl(userStore.userInfo.avatar) || defaultAvatar
})

// 兴趣爱好（添加emoji）
const selectedInterests = ref([])
const interestOptions = [
  { id: 1, name: '电影', emoji: '🎬' },
  { id: 2, name: '音乐', emoji: '🎵' },
  { id: 3, name: '旅行', emoji: '✈️' },
  { id: 4, name: '美食', emoji: '🍜' },
  { id: 5, name: '运动', emoji: '⚽' },
  { id: 6, name: '阅读', emoji: '📚' },
  { id: 7, name: '摄影', emoji: '📷' },
  { id: 8, name: '游戏', emoji: '🎮' },
  { id: 9, name: '健身', emoji: '💪' },
  { id: 10, name: '购物', emoji: '🛍️' },
  { id: 11, name: '宠物', emoji: '🐕' },
  { id: 12, name: '科技', emoji: '💻' }
]

// 生成年份列表
const years = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let year = currentYear - 60; year <= currentYear - 16; year++) {
    years.push(year)
  }
  return years.reverse()
})

// 计算年龄
const calculateAge = () => {
  const today = new Date()
  const birthDate = new Date(form.year, form.month - 1, form.day)
  let age = today.getFullYear() - birthDate.getFullYear()
  const monthDiff = today.getMonth() - birthDate.getMonth()
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--
  }
  
  return age
}

// 当前步骤是否可以继续
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 0:
      return form.nickname.trim() && form.gender
    case 1:
      return form.year && form.month && form.day
    case 2:
      return form.provinceCode.trim() !== ''
    case 3:
    case 4:
      return true
    case 5:
      return form.bio.trim() !== ''
    default:
      return false
  }
})

// 表单验证
const isFormValid = computed(() => {
  return form.nickname.trim() && form.gender && form.provinceCode.trim() !== ''
})

// 处理地区变化
const handleRegionChange = (regionData) => {
  form.provinceCode = regionData.provinceCode
  form.cityCode = regionData.cityCode
}

// 根据年月获取当月天数
const getDaysInMonth = (year, month) => {
  return new Date(year, month, 0).getDate()
}

// 切换兴趣标签
const toggleInterest = (id) => {
  const index = selectedInterests.value.indexOf(id)
  if (index === -1) {
    if (selectedInterests.value.length < 5) {
      selectedInterests.value.push(id)
    } else {
      toast.warning('最多只能选择5个兴趣标签')
    }
  } else {
    selectedInterests.value.splice(index, 1)
  }
}

// 下一步
const nextStep = () => {
  if (!canProceed.value) {
    return
  }
  
  if (currentStep.value < totalSteps - 1) {
    currentStep.value++
  }
}

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value.click()
}

// 处理文件选择
const handleFileChange = (event) => {
  const file = event.target.files[0]
  if (!file) {
    showOverlay.value = false
    return
  }

  if (!file.type.startsWith('image/')) {
    toast.error('请选择图片文件')
    showOverlay.value = false
    return
  }

  if (file.size > 2 * 1024 * 1024) {
    toast.error('图片大小不能超过2MB')
    showOverlay.value = false
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    previewUrl.value = e.target.result
    form.avatarUrl = e.target.result
    showOverlay.value = false
  }
  reader.readAsDataURL(file)
}

// 完成设置
const handleComplete = async () => {
  if (!isFormValid.value) {
    toast.warning('请填写必要信息')
    return
  }

  try {
    const selectedInterestNames = selectedInterests.value
      .map(id => interestOptions.find(item => item.id === id)?.name)
      .filter(Boolean)

    // 准备要提交到后端的数据
    const profileData = {
      nickname: form.nickname,
      gender: form.gender,
      age: calculateAge(),
        provinceCode: form.provinceCode || '',
        cityCode: form.cityCode || '',
      tags: selectedInterestNames,
      bio: form.bio || ''
    }

    // 调用后端API更新用户资料
    const { userService } = await import('../services/userService')
    const response = await userService.updateProfile(profileData)

    // 如果上传了新头像，处理头像上传
    let avatarUrl = form.avatarUrl
    if (fileInput.value && fileInput.value.files[0] && previewUrl.value) {
      try {
        const avatarResponse = await userService.uploadAvatar(fileInput.value.files[0])
        avatarUrl = avatarResponse.url || avatarResponse.data?.url
      } catch (avatarError) {
        // 静默处理头像上传失败，但资料已保存
      }
    }

    // 更新本地用户信息
    const updatedInfo = {
      ...userStore.userInfo,
      ...profileData,
      avatar: avatarUrl,
      profileCompleted: true
    }

    userStore.setUserInfo(updatedInfo)
    toast.success('资料保存成功')
    
    setTimeout(() => {
      router.push('/mine')
    }, 800)
  } catch (error) {
    toast.error('保存失败，请重试')
  }
}

// 跳过设置
const handleSkip = () => {
  const updatedInfo = {
    ...userStore.userInfo,
    profileCompleted: null  // 使用null表示用户主动跳过，区别于false（未完成）
  }
  userStore.setUserInfo(updatedInfo)
  toast.info('已跳过资料完善，您可以稍后在设置中完善')
  router.push('/')
}
</script>

<style scoped>
.profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.floating-heart {
  position: absolute;
  font-size: 2rem;
  opacity: 0.1;
}

.heart-1 {
  top: 20%;
  left: 10%;
}

.heart-2 {
  top: 60%;
  right: 15%;
}

.heart-3 {
  bottom: 30%;
  left: 20%;
}

.profile-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 1.25rem;
  width: 100%;
  max-width: 420px;
  box-shadow: 
    0 32px 64px rgba(255, 154, 158, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  position: relative;
  min-height: 420px;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
}

.profile-header {
  text-align: center;
  margin-bottom: 0.75rem;
  flex-shrink: 0;
}

.logo-container {
  margin-bottom: 0.5rem;
}

.logo {
  width: 45px;
  height: 45px;
  margin: 0 auto;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(255, 107, 107, 0.3);
}

.profile-header h2 {
  font-size: 1.4rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 0.15rem;
}

.profile-header p {
  color: #666;
  font-size: 0.85rem;
  font-weight: 500;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.25rem;
  flex-shrink: 0;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
  border-radius: 3px;
}

.progress-text {
  font-size: 0.85rem;
  font-weight: 600;
  color: #ff6b6b;
}

.form-container {
  flex: 1;
  display: flex;
  align-items: flex-start;
  overflow: hidden;
  margin-bottom: 1rem;
}

.step-content {
  width: 100%;
  max-height: 100%;
  overflow-y: auto;
}

.welcome-text {
  text-align: center;
  margin-bottom: 1.25rem;
}

.welcome-text h3 {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 0.15rem;
  font-weight: 600;
}

.welcome-text p {
  color: #666;
  font-size: 0.8rem;
}

.step-icon {
  font-size: 2.25rem;
  text-align: center;
  margin-bottom: 0.75rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.4rem;
  display: block;
  font-size: 0.95rem;
}

.step-description {
  color: #888;
  font-size: 0.8rem;
  margin-bottom: 1rem;
  text-align: center;
}

.input-container {
  position: relative;
}

.input-container input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid rgba(255, 107, 107, 0.2);
  border-radius: 16px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.8);
}

.input-container input:focus {
  outline: none;
  border-color: #ff6b6b;
  background: white;
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
}

.gender-selection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.gender-card {
  position: relative;
  padding: 1rem 0.75rem;
  background: rgba(255, 255, 255, 0.6);
  border: 2px solid rgba(255, 107, 107, 0.2);
  border-radius: 20px;
  text-align: center;
  cursor: pointer;
  overflow: hidden;
  color: #333;
}

.gender-card:hover {
  border-color: #ff6b6b;
  background: #fff;
  color: #333;
}

.gender-card.selected {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
  border-color: #ff6b6b;
}

.gender-emoji {
  font-size: 1.8rem;
  margin-bottom: 0.2rem;
}

.gender-card span {
  font-weight: 600;
  font-size: 0.85rem;
}

.selection-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.gender-card.selected .selection-indicator {
  background: white;
  border-color: white;
}

.date-picker {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 0.4rem;
}

.date-item {
  text-align: center;
}

.date-item label {
  font-size: 0.7rem;
  color: #888;
  margin-bottom: 0.2rem;
  font-weight: 500;
}

.date-select {
  width: 100%;
  padding: 0.5rem;
  border: 2px solid rgba(255, 107, 107, 0.2);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  font-size: 0.8rem;
}

.date-select:focus {
  outline: none;
  border-color: #ff6b6b;
  background: white;
}

.age-display {
  text-align: center;
  margin-top: 0.75rem;
  font-size: 0.95rem;
  color: #666;
}

.age-number {
  font-weight: 700;
  color: #ff6b6b;
  font-size: 1.1rem;
}

.avatar-upload-area {
  display: flex;
  justify-content: center;
  margin-bottom: 0.5rem;
}

.avatar-preview-large {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
  overflow: hidden;
  border: 4px solid rgba(255, 107, 107, 0.2);
}

.avatar-preview-large:hover {
  border-color: #ff6b6b;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 107, 107, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  color: white;
  pointer-events: none;
}

.upload-overlay.visible {
  opacity: 1;
}

.upload-icon {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.upload-overlay span {
  font-size: 0.8rem;
  font-weight: 600;
}

.upload-tips {
  text-align: center;
  font-size: 0.7rem;
  color: #888;
}

.upload-tips p {
  margin: 0.1rem 0;
}

.interests-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(85px, 1fr));
  gap: 0.4rem;
  margin-bottom: 0.5rem;
}

.interest-bubble {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.6rem 0.4rem;
  background: rgba(255, 255, 255, 0.6);
  border: 2px solid rgba(255, 107, 107, 0.2);
  border-radius: 16px;
  cursor: pointer;
  text-align: center;
  color: #333;
}

.interest-bubble:hover:not(.disabled) {
  border-color: #ff6b6b;
  background: #fff;
  color: #333;
}

.interest-bubble.selected {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e) !important;
  color: white !important;
  border-color: #ff6b6b !important;
}

.interest-bubble.selected .interest-name {
  color: white !important;
}

.interest-bubble.selected .interest-emoji {
  opacity: 1;
}

.interest-bubble.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  color: #999;
}

.interest-emoji {
  font-size: 1.1rem;
  margin-bottom: 0.2rem;
}

.interest-name {
  font-size: 0.75rem;
  font-weight: 600;
  color: inherit;
}

.selection-counter {
  text-align: center;
  font-size: 0.8rem;
  color: #666;
}

.counter-number {
  font-weight: 700;
  color: #ff6b6b;
}

.bio-container {
  position: relative;
}

.bio-textarea {
  width: 100%;
  padding: 0.6rem;
  border: 2px solid rgba(255, 107, 107, 0.2);
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
  line-height: 1.3;
  resize: none;
  font-family: inherit;
  height: 80px;
}

.bio-textarea:focus {
  outline: none;
  border-color: #ff6b6b;
  background: white;
}

.char-counter {
  position: absolute;
  bottom: 0.4rem;
  right: 0.6rem;
  font-size: 0.7rem;
  color: #888;
}

.char-counter .warning {
  color: #ff6b6b;
  font-weight: 600;
}

.bio-tips {
  margin-top: 0.4rem;
  font-size: 0.75rem;
  color: #888;
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: 0.6rem;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  min-height: 50px;
}

.btn {
  padding: 0.65rem 1.25rem;
  border: none;
  border-radius: 16px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  min-width: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.4rem;
  flex: 0 0 auto;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.8);
  color: #666;
  border: 2px solid rgba(255, 107, 107, 0.2);
  order: 1;
}

.btn-secondary:hover {
  background: white;
  color: #333;
}

.btn-primary, .btn-complete {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
  order: 2;
  flex: 1;
  max-width: 160px;
}

.btn-primary:hover:not(:disabled), .btn-complete:hover:not(:disabled) {
  box-shadow: 0 8px 24px rgba(255, 107, 107, 0.4);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.skip-section {
  text-align: center;
  flex-shrink: 0;
  min-height: 25px;
  margin-top: 0.5rem;
}

.skip-link {
  background: none;
  border: none;
  color: #999;
  font-size: 0.8rem;
  cursor: pointer;
  text-decoration: underline;
}

.skip-link:hover {
  color: #666;
}

.region-container {
  margin-top: 20px;
  width: 100%;
}

.region-tips {
  margin-top: 10px;
  font-size: 14px;
  color: #888;
}

@media (max-width: 480px) {
  .profile-card {
    padding: 1rem;
    margin: 10px;
    border-radius: 20px;
    min-height: 420px;
  }
  
  .form-container {
    margin-bottom: 0.75rem;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 0.6rem;
    justify-content: center;
    min-height: 80px;
  }
  
  .btn {
    width: 100%;
    padding: 0.75rem 1rem;
    order: unset;
    max-width: none;
  }
  
  .btn-secondary {
    order: 2;
  }
  
  .btn-primary, .btn-complete {
    order: 1;
  }
  
  .welcome-text {
    margin-bottom: 1rem;
  }
  
  .step-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }
  
  .gender-card {
    padding: 0.85rem 0.5rem;
  }
  
  .date-picker {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 0.2rem;
  }
  
  .interests-container {
    grid-template-columns: repeat(auto-fit, minmax(75px, 1fr));
    gap: 0.3rem;
  }
  
  .interest-bubble {
    padding: 0.5rem 0.3rem;
  }
}

@media (max-height: 700px) {
  .profile-card {
    padding: 1rem;
    min-height: 380px;
    max-height: 80vh;
  }
  
  .form-container {
    margin-bottom: 0.75rem;
  }
  
  .action-buttons {
    min-height: 45px;
  }
  
  .skip-section {
    min-height: 20px;
    margin-top: 0.25rem;
  }
  
  .progress-container {
    margin-bottom: 1rem;
  }
}

@media (max-height: 600px) {
  .profile-card {
    padding: 0.75rem;
    min-height: 340px;
  }
  
  .profile-header {
    margin-bottom: 0.5rem;
  }
  
  .logo {
    width: 40px;
    height: 40px;
  }
  
  .profile-header h2 {
    font-size: 1.2rem;
  }
  
  .progress-container {
    margin-bottom: 0.75rem;
  }
  
  .form-container {
    margin-bottom: 0.5rem;
  }
  
  .action-buttons {
    min-height: 40px;
  }
  
  .skip-section {
    min-height: 20px;
    margin-top: 0.25rem;
  }
  
  .step-icon {
    font-size: 1.8rem;
    margin-bottom: 0.4rem;
  }
  
  .form-group {
    margin-bottom: 0.75rem;
  }
}
</style>