<template>
  <page-layout title="帖子详情" :show-back="true" hide-tab-bar>
    <div class="post-detail-container">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-container">
        <div class="loading-spinner" />
        <div class="loading-text">加载中...</div>
      </div>

      <!-- 帖子内容 -->
      <template v-else-if="post">
        <div class="post-content-section">
          <post-card
            :post="post"
            :show-full-content="true"
            @like="likePost"
            @comment="scrollToCommentInput"
            @share="sharePost"
            @toggle-privacy="togglePostPrivacy"
            @delete="deletePost"
          />
        </div>

        <!-- 评论区 -->
        <div class="comments-section">
          <div class="comments-header">
            <h3>评论 ({{ comments.length }})</h3>
            <div class="comments-sort">
              <span :class="{ active: sortBy === 'latest' }" @click="setSortBy('latest')">
                最新
              </span>
              <span :class="{ active: sortBy === 'hot' }" @click="setSortBy('hot')">热门</span>
            </div>
          </div>

          <!-- 评论列表 -->
          <div v-if="comments.length > 0" class="comments-list">
            <comment-item
              v-for="comment in sortedComments"
              :key="comment.id"
              :comment="comment"
              @like="likeComment"
              @reply="replyToComment"
              @delete="deleteComment"
              @preview-image="previewImage"
            />
          </div>

          <!-- 没有评论时显示 -->
          <div v-else class="no-comments">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="40"
              height="40"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="1"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
            </svg>
            <p>暂无评论，快来发表第一条评论吧</p>
          </div>
        </div>
      </template>

      <!-- 帖子不存在 -->
      <div v-else class="not-found-container">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="60"
          height="60"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="1"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <circle cx="12" cy="12" r="10" />
          <line x1="12" y1="8" x2="12" y2="12" />
          <line x1="12" y1="16" x2="12.01" y2="16" />
        </svg>
        <p>帖子不存在或已被删除</p>
        <div class="back-button" @click="router.back()">返回</div>
      </div>
    </div>

    <!-- 评论输入框 -->
    <div v-if="post" ref="commentInputRef" class="comment-input-container">
      <div class="comment-input-wrapper" :class="{ 'with-reply': replyTarget }">
        <div v-if="replyTarget" class="reply-target">
          回复
          <span>@{{ replyTarget.nickname || replyTarget.username }}</span>
          <div class="cancel-reply" @click="cancelReply">×</div>
        </div>
        <div class="input-row">
          <textarea
            ref="textareaRef"
            v-model="commentContent"
            placeholder="写下你的评论..."
            class="comment-input"
            @input="adjustTextareaHeight"
          />
          <div
            class="comment-submit"
            :class="{ active: commentContent.trim().length > 0 }"
            @click="submitComment"
          >
            发送
          </div>
        </div>

        <!-- 功能按钮区域 -->
        <div class="comment-tools">
          <div class="tool-item" @click.stop="showEmojiPicker = !showEmojiPicker">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <circle cx="12" cy="12" r="10" />
              <path d="M8 14s1.5 2 4 2 4-2 4-2" />
              <line x1="9" y1="9" x2="9.01" y2="9" />
              <line x1="15" y1="9" x2="15.01" y2="9" />
            </svg>
          </div>
          <div class="tool-item" @click="triggerImageUpload">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <rect x="3" y="3" width="18" height="18" rx="2"
ry="2" />
              <circle cx="8.5" cy="8.5" r="1.5" />
              <polyline points="21 15 16 10 5 21" />
            </svg>
            <input
              ref="imageInputRef"
              type="file"
              accept="image/*"
              style="display: none"
              @change="handleImageSelect"
            />
          </div>
        </div>

        <!-- 表情选择器 -->
        <div class="emoji-panel-wrapper" @click.stop>
          <emoji-panel v-model="showEmojiPicker" @select="insertEmoji" />
        </div>

        <!-- 已选图片预览 -->
        <div v-if="selectedImage" class="selected-image-preview">
          <img v-lazy="selectedImage.url" alt="图片预览" @click="previewImage(selectedImage.url)" />
          <div class="image-delete" @click="removeImage">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18" />
              <line x1="6" y1="6" x2="18" y2="18" />
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览 -->
    <transition name="fade">
      <div v-if="previewImageUrl" class="image-preview" @click="closePreview">
        <div v-if="previewLoading" class="image-preview-loading" />
        <img
          v-lazy="previewImageUrl"
          :class="{ loaded: !previewLoading }"
          @load="previewLoading = false"
        />
      </div>
    </transition>
  </page-layout>
</template>

<script setup>
  import { ref, computed, onMounted, nextTick, inject } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useUserStore } from '../stores/userStore.js'
  import PageLayout from '../components/PageLayout.vue'
  import PostCard from '../components/PostCard.vue'
  import CommentItem from '../components/CommentItem.vue'
  import EmojiPanel from '../components/EmojiPanel.vue'
  import { postService } from '../services/postService.js'
  import { uploadService } from '../services/uploadService.js'

  const route = useRoute()
  const router = useRouter()
  const userStore = useUserStore()
  const toast = inject('toast')

  // 获取帖子ID
  const postId = ref(Number(route.params.id))

  // 加载状态
  const isLoading = ref(true)

  // 帖子数据
  const post = ref(null)

  // 评论列表
  const comments = ref([])

  // 评论排序
  const sortBy = ref('latest')

  // 评论内容
  const commentContent = ref('')
  const textareaRef = ref(null)
  const commentInputRef = ref(null)

  // 回复目标
  const replyTarget = ref(null)

  // 表情选择器
  const showEmojiPicker = ref(false)

  // 已选图片
  const selectedImage = ref(null)
  const imageInputRef = ref(null)

  // 图片预览相关
  const previewImageUrl = ref(null)
  const previewLoading = ref(true)

  // 获取帖子数据
  const fetchPostData = async () => {
    try {
      loading.value = true
      
      const response = await postService.getPostDetail(postId.value)
      
      // 提取帖子数据
      let postData
      if (response.data) {
        postData = response.data
      } else if (response.id) {
        postData = response
      } else {
        throw new Error('无效的响应数据')
      }
      
      if (postData && postData.id) {
        // 处理帖子数据
        post.value = {
          id: postData.id,
          content: postData.content || '',
          images: postData.images || [],
          user: {
            id: postData.user?.id || postData.userId,
            nickname: postData.user?.nickname || '匿名用户',
            avatar: postData.user?.avatar || DEFAULT_AVATAR
          },
          likeCount: postData.likeCount || 0,
          commentCount: postData.commentCount || 0,
          shareCount: postData.shareCount || 0,
          isLiked: postData.isLiked || false,
          isPrivate: postData.isPrivate || false,
          createTime: new Date(postData.createTime || postData.createdAt),
          isMine: postData.user?.id === userStore.userInfo.id
        }
        
        // 处理评论数据
        if (postData.comments && Array.isArray(postData.comments)) {
          comments.value = postData.comments.map(comment => ({
            id: comment.id,
            content: comment.content,
            user: {
              id: comment.user?.id || comment.userId,
              nickname: comment.user?.nickname || '匿名用户',
              avatar: comment.user?.avatar || DEFAULT_AVATAR
            },
            likeCount: comment.likeCount || 0,
            isLiked: comment.isLiked || false,
            createTime: new Date(comment.createTime || comment.createdAt),
            isMine: comment.user?.id === userStore.userInfo.id
          }))
        }
      } else {
        throw new Error('无法获取帖子数据')
      }
    } catch (error) {
      toast.error('获取帖子详情失败')
      router.back()
    } finally {
      loading.value = false
    }
  }

  // 获取评论列表
  const fetchComments = async () => {
    try {
      const response = await postService.getPostComments(postId.value)
      const commentsData = response.data || response || []
      
      comments.value = commentsData.map(comment => ({
        id: comment.id,
        content: comment.content,
        user: {
          id: comment.user?.id || comment.userId,
          nickname: comment.user?.nickname || '匿名用户',
          avatar: comment.user?.avatar || DEFAULT_AVATAR
        },
        likeCount: comment.likeCount || 0,
        isLiked: comment.isLiked || false,
        createTime: new Date(comment.createTime || comment.createdAt),
        isMine: comment.user?.id === userStore.userInfo.id
      }))
    } catch (error) {
      // 静默处理获取评论失败
    }
  }

  // 计算排序后的评论
  const sortedComments = computed(() => {
    if (!comments.value || comments.value.length === 0) {
      return []
    }
    
    let sorted = [...comments.value]
    
    if (sortBy.value === 'time') {
      sorted.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
    } else if (sortBy.value === 'hot') {
      sorted.sort((a, b) => b.likeCount - a.likeCount)
    }
    
    return sorted
  })

  // 设置排序方式
  const setSortBy = (type) => {
    if (sortBy.value === type) return
    
    sortBy.value = type
    // 由于目前后端没有单独的评论排序接口，这里只改变本地排序
    // 如果后端支持排序参数，可以调用 fetchComments()
  }

  // 点赞帖子
  const likePost = async (id) => {
    if (!post.value || post.value.id !== id) return

    try {
      const action = post.value.isLiked ? 'unlike' : 'like'
      await postService.likePost(id, action)

      // 更新本地状态
      if (post.value.isLiked) {
        post.value.likeCount--
      } else {
        post.value.likeCount++
      }
      post.value.isLiked = !post.value.isLiked
    } catch (error) {
      toast.error('点赞操作失败')
    }
  }

  // 分享帖子
  const sharePost = async (id) => {
    if (!post.value) return
    
    try {
      await postService.sharePost(id)
      post.value.shareCount += 1
      toast.success('分享成功')
    } catch (error) {
      toast.error('分享失败')
    }
  }

  // 切换帖子隐私状态
  const togglePostPrivacy = async (id) => {
    if (!post.value || post.value.id !== id || !post.value.isMine) return

    try {
      const newPrivacy = !post.value.isPrivate
      await postService.setPostPrivacy(id, newPrivacy)

      // 更新本地状态
      post.value.isPrivate = newPrivacy
      
      if (newPrivacy) {
        toast.success('帖子已设为私密')
      } else {
        toast.success('帖子已设为公开')
      }
    } catch (error) {
      toast.error('设置隐私状态失败')
    }
  }

  // 删除帖子
  const deletePost = async (id) => {
    if (!post.value || post.value.id !== id) return

    try {
      await postService.deletePost(id)
      
      toast.success('帖子已删除')
      router.back()
    } catch (error) {
      toast.error('删除帖子失败')
    }
  }

  // 点赞评论
  const likeComment = async (id) => {
    const comment = comments.value.find(c => c.id === id)
    if (!comment) return

    try {
      const action = comment.isLiked ? 'unlike' : 'like'
      await postService.likeComment(id, action)

      // 更新本地状态
      if (comment.isLiked) {
        comment.likeCount--
      } else {
        comment.likeCount++
      }
      comment.isLiked = !comment.isLiked
    } catch (error) {
      toast.error('点赞评论失败')
    }
  }

  // 回复评论
  const replyToComment = (id, username) => {
    const comment = comments.value.find(c => c.id === id)
    if (comment) {
      replyTarget.value = {
        id,
        username
      }

      // 聚焦到评论输入框
      nextTick(() => {
        textareaRef.value.focus()
      })
    }
  }

  // 取消回复
  const cancelReply = () => {
    replyTarget.value = null
  }

  // 删除评论
  const deleteComment = async (id) => {
    try {
      await postService.deleteComment(id)
      
      // 从本地列表中移除
      const index = comments.value.findIndex(c => c.id === id)
      if (index !== -1) {
        comments.value.splice(index, 1)
      }
      
      // 更新帖子评论数
      if (post.value) {
        post.value.commentCount--
      }
      
      toast.success('评论已删除')
    } catch (error) {
      toast.error('删除评论失败')
    }
  }

  // 提交评论
  const submitComment = async () => {
    if (commentContent.value.trim().length === 0 && !selectedImage.value) {
      toast.warning('请输入评论内容或选择图片')
      return
    }

    try {
      submitting.value = true
      
      // 上传图片
      let imageUrls = []
      if (selectedImage.value) {
        try {
          uploadService.validateFile(selectedImage.value.file)
          const uploadResult = await uploadService.uploadFile(selectedImage.value.file, 'message')
          if (uploadResult && uploadResult.url) {
            imageUrls.push(uploadResult.url)
          }
        } catch (error) {
          toast.error('图片上传失败')
          throw error
        }
      }
      
      // 提交评论
      const commentData = {
        postId: postId.value,
        content: commentContent.value.trim(),
        images: imageUrls
      }
      
      const response = await postService.createComment(commentData)
      
      // 提取评论响应数据
      let commentResponseData
      if (response.data) {
        commentResponseData = response.data
      } else if (response.id) {
        commentResponseData = response
      }
      
      // 创建新评论对象
      if (commentResponseData && commentResponseData.id) {
        const newCommentObj = {
          id: commentResponseData.id,
          content: commentResponseData.content || commentContent.value,
          user: {
            id: userStore.userInfo.id,
            nickname: userStore.userInfo.nickname || userStore.userInfo.username,
            avatar: userStore.userInfo.avatar || DEFAULT_AVATAR
          },
          likeCount: 0,
          isLiked: false,
          createTime: new Date(),
          isMine: true,
          images: imageUrls
        }
        
        comments.value.push(newCommentObj)
        
        // 更新帖子评论数
        if (post.value) {
          post.value.commentCount += 1
        }
        
        await nextTick()
      } else {
        // 使用 fallback 数据创建评论
        const fallbackComment = {
          id: Date.now().toString(),
          content: commentContent.value,
          user: {
            id: userStore.userInfo.id,
            nickname: userStore.userInfo.nickname || userStore.userInfo.username,
            avatar: userStore.userInfo.avatar || DEFAULT_AVATAR
          },
          likeCount: 0,
          isLiked: false,
          createTime: new Date(),
          isMine: true,
          images: imageUrls
        }
        
        comments.value.push(fallbackComment)
        
        if (post.value) {
          post.value.commentCount += 1
        }
        
        await nextTick()
      }
      
      // 清空输入
      commentContent.value = ''
      selectedImage.value = null
      replyTarget.value = null
      adjustTextareaHeight()
      
      toast.success('评论发表成功')
    } catch (error) {
      toast.error('发送评论失败')
    } finally {
      submitting.value = false
    }
  }

  // 调整文本框高度
  const adjustTextareaHeight = () => {
    if (!textareaRef.value) {
      return
    }

    textareaRef.value.style.height = 'auto'
    textareaRef.value.style.height = `${textareaRef.value.scrollHeight}px`
  }

  // 滚动到评论输入框
  const scrollToCommentInput = () => {
    commentInputRef.value.scrollIntoView({ behavior: 'smooth' })
    textareaRef.value.focus()
  }

  // 触发图片上传
  const triggerImageUpload = () => {
    imageInputRef.value.click()
  }

  // 处理图片选择
  const handleImageSelect = event => {
    const file = event.target.files[0]
    if (!file) {
      return
    }

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      alert('请选择图片文件')
      return
    }

    // 使用FileReader预览图片
    const reader = new FileReader()
    reader.onload = e => {
      selectedImage.value = {
        file,
        url: e.target.result
      }
    }
    reader.readAsDataURL(file)

    // 重置文件输入
    event.target.value = ''
  }

  // 插入表情
  const insertEmoji = emoji => {
    const textarea = textareaRef.value
    const startPos = textarea.selectionStart
    const endPos = textarea.selectionEnd

    const beforeText = commentContent.value.substring(0, startPos)
    const afterText = commentContent.value.substring(endPos)

    commentContent.value = beforeText + emoji + afterText

    // 设置光标位置
    nextTick(() => {
      const newCursorPos = startPos + emoji.length
      textarea.focus()
      textarea.setSelectionRange(newCursorPos, newCursorPos)
    })

    // 不再自动关闭表情选择器，允许连续选择
    // showEmojiPicker.value = false
  }

  // 移除图片
  const removeImage = () => {
    selectedImage.value = null
  }

  // 预览图片
  const previewImage = url => {
    previewImageUrl.value = url
    previewLoading.value = true
  }

  // 关闭预览
  const closePreview = () => {
    previewImageUrl.value = null
  }

  // 组件挂载时
  onMounted(() => {
    fetchPostData() // 这个函数已经包含了评论数据的获取
  })
</script>

<style scoped>
  .post-detail-container {
    padding-bottom: 80px;
  }

  .post-content-section {
    margin-bottom: 15px;
  }

  .comments-section {
    background-color: #fff;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .comments-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .comments-header h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
  }

  .comments-sort {
    display: flex;
    gap: 16px;
  }

  .comments-sort span {
    font-size: 14px;
    color: #666;
    cursor: pointer;
  }

  .comments-sort span.active {
    color: var(--color-primary);
    font-weight: 500;
  }

  .comments-list {
    margin-top: 10px;
  }

  .no-comments {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    color: #999;
  }

  .no-comments svg {
    margin-bottom: 15px;
    stroke: #ccc;
  }

  .no-comments p {
    font-size: 14px;
  }

  .comment-input-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    padding: 12px 16px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
  }

  .comment-input-wrapper {
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
    border-radius: 18px;
    padding: 8px 12px;
    position: relative;
  }

  .comment-input-wrapper.with-reply {
    padding-top: 4px;
  }

  .reply-target {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
    padding: 0 4px;
  }

  .reply-target span {
    color: #1890ff;
    margin: 0 4px;
    font-weight: 500;
  }

  .cancel-reply {
    font-size: 16px;
    margin-left: auto;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.05);
    cursor: pointer;
  }

  .cancel-reply:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }

  .input-row {
    display: flex;
    align-items: flex-end;
    width: 100%;
  }

  .comment-input {
    flex: 1;
    border: none;
    background: transparent;
    resize: none;
    max-height: 80px;
    min-height: 20px;
    font-size: 14px;
    line-height: 1.5;
    outline: none;
    padding: 4px;
  }

  .comment-submit {
    padding: 6px 12px;
    border-radius: 16px;
    background-color: #e0e0e0;
    color: #999;
    font-size: 14px;
    margin-left: 8px;
    cursor: not-allowed;
    user-select: none;
  }

  .comment-submit.active {
    background-color: var(--color-primary);
    color: #fff;
    cursor: pointer;
  }

  .comment-tools {
    display: flex;
    padding: 4px;
    margin-top: 4px;
  }

  .tool-item {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-right: 8px;
    cursor: pointer;
    color: #666;
  }

  .tool-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .selected-image-preview {
    margin-top: 8px;
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    max-height: 200px;
  }

  .selected-image-preview img {
    width: 100%;
    object-fit: contain;
    cursor: pointer;
  }

  .image-delete {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .loading-text {
    margin-top: 10px;
    font-size: 14px;
    color: #666;
  }

  .not-found-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
  }

  .not-found-container svg {
    width: 60px;
    height: 60px;
    margin-bottom: 10px;
  }

  .not-found-container p {
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
  }

  .back-button {
    padding: 10px 20px;
    background-color: var(--color-primary);
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .emoji-panel-wrapper {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    z-index: 101; /* 确保表情面板在最上层 */
    margin-bottom: 10px;
  }

  /* 图片预览样式 */
  .image-preview {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .image-preview img {
    max-width: 90%;
    max-height: 90%;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .image-preview img.loaded {
    opacity: 1;
  }

  .image-preview-loading {
    width: 48px;
    height: 48px;
    position: absolute;
    border: 3px solid transparent;
    border-top-color: white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  /* 过渡效果 */
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
</style>
