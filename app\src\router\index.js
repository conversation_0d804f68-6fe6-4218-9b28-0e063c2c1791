import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../stores/userStore'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue'),
    meta: {
      title: '推荐',
      requiresAuth: true
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: {
      title: '登录'
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/Register.vue'),
    meta: {
      title: '注册'
    }
  },
  {
    path: '/message',
    name: 'Message',
    component: () => import('../views/Message.vue'),
    meta: {
      title: '消息中心',
      requiresAuth: true
    }
  },
  {
    path: '/chat/:id',
    name: 'Chat',
    component: () => import('../views/Chat.vue'),
    meta: {
      title: '聊天',
      requiresAuth: true
    }
  },
  {
    path: '/likes',
    name: 'Likes',
    component: () => import('../views/Likes.vue'),
    meta: {
      title: '喜欢',
      requiresAuth: true
    }
  },
  {
    path: '/mine',
    name: 'Mine',
    component: () => import('../views/Mine.vue'),
    meta: {
      title: '我的',
      requiresAuth: true
    }
  },
  {
    path: '/profile/:id',
    name: 'Profile',
    component: () => import('../views/Profile.vue'),
    meta: {
      title: '用户资料',
      requiresAuth: true
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('../views/Settings.vue'),
    meta: {
      title: '设置',
      requiresAuth: true
    }
  },
  {
    path: '/change-password',
    name: 'ChangePassword',
    component: () => import('../views/ChangePassword.vue'),
    meta: {
      title: '修改密码',
      requiresAuth: true
    }
  },
  {
    path: '/notification-settings',
    name: 'NotificationSettings',
    component: () => import('../views/NotifySettings.vue'),
    meta: {
      title: '通知管理',
      requiresAuth: true
    }
  },
  {
    path: '/my-profile',
    name: 'MyProfile',
    component: () => import('../views/MyProfile.vue'),
    meta: {
      title: '我的资料',
      requiresAuth: true
    }
  },
  {
    path: '/complete-profile',
    name: 'CompleteProfile',
    component: () => import('../views/NewProfile.vue'),
    meta: {
      title: '完善资料',
      requiresAuth: true
    }
  },
  {
    path: '/square',
    name: 'Square',
    component: () => import('../views/Square.vue'),
    meta: {
      title: '广场',
      requiresAuth: true
    }
  },
  {
    path: '/create-post',
    name: 'CreatePost',
    component: () => import('../views/CreatePost.vue'),
    meta: {
      title: '发布动态',
      requiresAuth: true
    }
  },
  {
    path: '/post/:id',
    name: 'PostDetail',
    component: () => import('../views/PostDetail.vue'),
    meta: {
      title: '帖子详情',
      requiresAuth: true
    }
  },
  {
    path: '/visitors',
    name: 'Visitors',
    component: () => import('../views/Visitors.vue'),
    meta: {
      title: '访客记录',
      requiresAuth: true
    }
  },
  {
    path: '/terms-of-service',
    name: 'TermsOfService',
    component: () => import('../views/TermsOfService.vue'),
    meta: {
      title: '服务条款',
      requiresAuth: true
    }
  },
  
  {
    path: '/404',
    name: '404',
    component: () => import('../views/404.vue'),
    meta: {
      title: '页面不存在'
    }
  },
  {
    path: '/:pathMatch(.*)',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  document.title = to.meta.title || '移动应用'

  // 检查该路由是否需要登录权限
  if (to.matched.some(record => record.meta.requiresAuth)) {
    // 获取用户登录状态
    const userStore = useUserStore()
    if (!userStore.isLogin) {
      // 未登录，跳转到登录页
      next({
        path: '/login',
        // 保存原本要去的路径，登录成功后可以直接跳转
        query: { redirect: to.fullPath }
      })
    } else {
      // 已登录，检查是否已完善资料
      const userInfo = userStore.userInfo

      // 如果用户未完善资料（false），且不是前往完善资料页面，则引导其完善资料
      // null表示用户主动跳过，允许访问其他页面
      if (
        userInfo.profileCompleted === false &&
        to.name !== 'CompleteProfile' &&
        to.name !== 'Settings' &&
        to.name !== 'Logout'
      ) {
        next({ path: '/complete-profile' })
      } else {
        next()
      }
    }
  } else {
    next()
  }
})

export default router
