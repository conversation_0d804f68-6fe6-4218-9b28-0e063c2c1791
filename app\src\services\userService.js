import { http } from '../utils/request.js'
import { uploadService } from './uploadService.js'

/**
 * 用户相关 API 服务
 */
export const userService = {
  /**
   * 获取用户资料
   * @returns {Promise}
   */
  async getProfile() {
    try {
      const response = await http.get('/user/profile')
      return response
    } catch (error) {
      throw new Error(error.message || '获取用户资料失败')
    }
  },

  /**
   * 更新用户资料
   * @param {Object} profileData - 用户资料数据
   * @returns {Promise}
   */
  async updateProfile(profileData) {
    try {
      // 确保包含省市代码字段
      const requestData = {
        ...profileData
      }
      
      // 如果有省市代码，优先使用代码格式
      if (profileData.provinceCode || profileData.cityCode) {
        requestData.provinceCode = profileData.provinceCode || ''
        requestData.cityCode = profileData.cityCode || ''
      }
      
      const response = await http.put('/user/profile', requestData)
      return response
    } catch (error) {
      throw new Error(error.message || '更新用户资料失败')
    }
  },

  /**
   * 上传头像
   * @param {File} avatarFile - 头像文件
   * @returns {Promise}
   */
  async uploadAvatar(avatarFile) {
    return await uploadService.uploadAvatar(avatarFile)
  },

  /**
   * 上传相册
   * @param {File[]} photoFiles - 相册文件数组
   * @returns {Promise}
   */
  async uploadPhotos(photoFiles) {
    return await uploadService.uploadPhotos(photoFiles)
  },

  /**
   * 设置头像
   * @param {string} photoUrl - 要设置为头像的照片URL
   * @returns {Promise}
   */
  async setAvatar(photoUrl) {
    try {
      const response = await http.put('/user/avatar', { photoUrl })
      return response
    } catch (error) {
      throw new Error(error.message || '设置头像失败')
    }
  },

  /**
   * 删除照片
   * @param {string} photoId - 照片ID或URL
   * @returns {Promise}
   */
  async deletePhoto(photoId) {
    try {
      const response = await http.delete(`/user/photos/${photoId}`)
      return response
    } catch (error) {
      throw new Error(error.message || '删除照片失败')
    }
  },

  /**
   * 获取其他用户资料
   * @param {number} userId - 用户ID
   * @returns {Promise}
   */
  async getUserProfile(userId) {
    try {
      const response = await http.get(`/user/profile/${userId}`)
      return response
    } catch (error) {
      throw new Error(error.message || '获取用户资料失败')
    }
  },

  /**
   * 获取用户相册
   * @param {number} userId - 用户ID
   * @returns {Promise}
   */
  async getUserPhotos(userId) {
    try {
      const response = await http.get(`/user/photos/${userId}`)
      return response
    } catch (error) {
      throw new Error(error.message || '获取用户相册失败')
    }
  },

  /**
   * 喜欢用户
   * @param {number} userId - 目标用户ID
   * @returns {Promise}
   */
  async likeUser(userId) {
    try {
      const response = await http.post('/match/like', { targetUserId: userId })
      return response
    } catch (error) {
      throw new Error(error.message || '喜欢用户失败')
    }
  },

  /**
   * 不喜欢用户
   * @param {number} userId - 目标用户ID
   * @returns {Promise}
   */
  async dislikeUser(userId) {
    try {
      const response = await http.post('/match/dislike', { targetUserId: userId })
      return response
    } catch (error) {
      throw new Error(error.message || '跳过用户失败')
    }
  },

  /**
   * 检查匹配状态
   * @param {number} userId - 目标用户ID
   * @returns {Promise<boolean>}
   */
  async checkMatchStatus(userId) {
    try {
      const response = await http.get(`/match/status/${userId}`)
      return response.isMatched || false
    } catch (error) {
      throw new Error(error.message || '检查匹配状态失败')
    }
  },

  /**
   * 举报用户
   * @param {number} userId - 目标用户ID
   * @param {string} reason - 举报原因
   * @returns {Promise}
   */
  async reportUser(userId, reason) {
    try {
      const response = await http.post('/user/report', { 
        targetUserId: userId, 
        reason 
      })
      return response
    } catch (error) {
      throw new Error(error.message || '举报用户失败')
    }
  },

  /**
   * 屏蔽用户
   * @param {number} userId - 目标用户ID
   * @returns {Promise}
   */
  async blockUser(userId) {
    try {
      const response = await http.post('/user/block', { targetUserId: userId })
      return response
    } catch (error) {
      throw new Error(error.message || '屏蔽用户失败')
    }
  },

  /**
   * 记录访问
   * @param {number} targetUserId - 目标用户ID
   * @returns {Promise}
   */
  async recordVisit(targetUserId) {
    try {
      const response = await http.post('/user/visit', { 
        targetUserId 
      })
      return response
    } catch (error) {
      throw new Error(error.message || '记录访问失败')
    }
  },

  /**
   * 获取访客列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   * @returns {Promise}
   */
  async getVisitors(params = {}) {
    try {
      const { page = 1, limit = 20 } = params
      const response = await http.get('/user/visitors', { 
        params: { page, limit }
      })
      return response
    } catch (error) {
      throw new Error(error.message || '获取访客列表失败')
    }
  },

  /**
   * 获取用户统计数据
   * @returns {Promise}
   */
  async getUserStats() {
    try {
      const response = await http.get('/user/stats')
      return response
    } catch (error) {
      throw new Error(error.message || '获取统计数据失败')
    }
  },

  /**
   * 获取省市数据
   * @param {string} provinceCode - 可选，如果提供则只获取该省份的城市
   * @returns {Promise<Object>} 省市数据
   */
  async getRegions(provinceCode) {
    try {
      let url = '/user/regions'
      if (provinceCode) {
        url += `?province=${provinceCode}`
      }
      const response = await http.get(url)
      return response
    } catch (error) {
      // 静默处理获取地区数据失败，返回空数组
      return []
    }
  }
} 