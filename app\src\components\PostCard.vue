<template>
  <div v-if="post" class="post-card">
    <!-- 用户信息 -->
    <div class="post-header">
      <div class="user-avatar" @click="navigateToProfile">
        <avatar :src="post.avatar" size="medium" />
      </div>
      <div class="user-info">
        <div class="username" @click="navigateToProfile">{{ post.nickname || post.username }}</div>
        <div class="post-meta">
          <span class="post-time">{{ formatTime(post.createTime) }}</span>
          <span v-if="post.location" class="post-location">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
              <circle cx="12" cy="10" r="3" />
            </svg>
            {{ post.location }}
          </span>
        </div>
      </div>

      <!-- 私密标记 - 移到右上角 -->
      <div v-if="post.isPrivate && post.isMine" class="privacy-indicator">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
          <path d="M7 11V7a5 5 0 0 1 10 0v4" />
        </svg>
        <span>私密</span>
      </div>
    </div>

    <!-- 帖子内容 -->
    <div v-if="post.content" class="post-content">
      {{ post.content }}
    </div>

    <!-- 帖子图片 -->
    <div v-if="post.images && post.images.length > 0" class="post-images" :class="imageLayoutClass">
      <div v-for="(image, index) in post.images" :key="index" class="post-image-item" @click="previewImage(image)">
        <img v-lazy="image" :alt="`图片${index + 1}`" />
      </div>
    </div>

    <!-- 帖子操作 -->
    <div class="post-actions">
      <div class="action-item" @click="handleLike">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
          :fill="post.isLiked ? '#ff6b6b' : 'none'" :stroke="post.isLiked ? '#ff6b6b' : 'currentColor'" stroke-width="2"
          stroke-linecap="round" stroke-linejoin="round">
          <path
            d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" />
        </svg>
        <span :class="{ liked: post.isLiked }">{{ post.likeCount }}</span>
      </div>
      <div class="action-item" @click="handleComment">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path
            d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z" />
        </svg>
        <span>{{ post.commentCount }}</span>
      </div>
      <div class="action-item" @click="handleShare">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="18" cy="5" r="3" />
          <circle cx="6" cy="12" r="3" />
          <circle cx="18" cy="19" r="3" />
          <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" />
          <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" />
        </svg>
        <span>{{ post.shareCount }}</span>
      </div>

      <!-- 更多操作按钮，仅对自己的帖子显示 -->
      <div v-if="post.isMine" class="action-item" @click="showMoreMenu = !showMoreMenu">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="1" />
          <circle cx="19" cy="12" r="1" />
          <circle cx="5" cy="12" r="1" />
        </svg>

        <!-- 更多操作菜单 -->
        <div v-if="showMoreMenu" class="more-menu">
          <div class="menu-item" @click.stop="togglePrivacy">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
              <path d="M7 11V7a5 5 0 0 1 10 0v4" />
            </svg>
            <span>{{ post.isPrivate ? '设为公开' : '设为私密' }}</span>
          </div>
          <div class="menu-item" @click.stop="handleDelete">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="3 6 5 6 21 6" />
              <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
              <line x1="10" y1="11" x2="10" y2="17" />
              <line x1="14" y1="11" x2="14" y2="17" />
            </svg>
            <span>删除</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-else class="post-card post-card-placeholder">
    <div class="placeholder-text">帖子加载中...</div>
  </div>

  <!-- 图片预览 -->
  <transition name="fade">
    <div v-if="previewImageUrl" class="image-preview" @click="closePreview">
      <div v-if="previewLoading" class="image-preview-loading" />
      <img v-lazy="previewImageUrl" :class="{ loaded: !previewLoading }" @load="previewLoading = false" />
    </div>
  </transition>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import Avatar from './Avatar.vue'

const router = useRouter()

// 定义props
const props = defineProps({
  post: {
    type: Object,
    required: true
  },
  showFullContent: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emit = defineEmits(['like', 'comment', 'share', 'toggle-privacy', 'delete'])

const showMoreMenu = ref(false)

// 图片预览相关
const previewImageUrl = ref(null)
const previewLoading = ref(true)

// 根据图片数量计算布局样式
const imageLayoutClass = computed(() => {
  if (!props.post.images || !Array.isArray(props.post.images)) {
    return ''
  }
  
  const count = props.post.images.length
  if (count === 1) {
    return 'single-image'
  }
  if (count === 2) {
    return 'double-image'
  }
  if (count === 3) {
    return 'triple-image'
  }
  if (count === 4) {
    return 'quad-image'
  }
  return 'multi-image'
})

// 格式化时间
const formatTime = dateInput => {
  if (!dateInput) return ''
  
  // 确保是Date对象
  const date = dateInput instanceof Date ? dateInput : new Date(dateInput)
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return ''
  }
  
  const now = new Date()
  const diff = now - date

  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }

  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 1000))}分钟前`
  }

  // 小于24小时
  if (diff < 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
  }

  // 小于30天
  if (diff < 30 * 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`
  }

  // 大于30天，显示具体日期
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
}

// 处理点赞
const handleLike = () => {
  emit('like', props.post.id)
}

// 处理评论
const handleComment = () => {
  if (props.showFullContent) {
    // 如果已经在详情页，直接触发评论事件
    emit('comment', props.post.id)
  } else {
    // 否则跳转到帖子详情页
    router.push(`/post/${props.post.id}`)
  }
}

// 处理分享
const handleShare = () => {
  emit('share', props.post.id)
}

// 处理隐私设置切换
const togglePrivacy = () => {
  emit('toggle-privacy', props.post.id)
  showMoreMenu.value = false
}

// 处理删除
const handleDelete = () => {
  if (confirm('确定要删除这篇帖子吗？')) {
    emit('delete', props.post.id)
  }
  showMoreMenu.value = false
}

// 预览图片
const previewImage = imageUrl => {
  previewImageUrl.value = imageUrl
  previewLoading.value = true
}

// 关闭预览
const closePreview = () => {
  previewImageUrl.value = null
}

// 导航到用户资料页
const navigateToProfile = () => {
  router.push(`/profile/${props.post.userId}`)
}

// 点击外部关闭菜单
const closeMenu = event => {
  if (
    showMoreMenu.value &&
    !event.target.closest('.more-menu') &&
    !event.target.closest('.action-item')
  ) {
    showMoreMenu.value = false
  }
}

// 添加和移除全局点击事件监听器
onMounted(() => {
  if (typeof window !== 'undefined') {
    window.addEventListener('click', closeMenu)
  }
})

onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('click', closeMenu)
  }
})
</script>

<style scoped>
.post-card {
  background-color: #fff;
  border-radius: var(--border-radius);
  padding: 16px;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.post-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  position: relative;
}

.user-avatar {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  cursor: pointer;
}

.user-avatar :deep(.avatar-wrapper) {
  width: 100%;
  height: 100%;
}

.user-avatar :deep(.avatar) {
  width: 100%;
  height: 100%;
}

.user-info {
  flex: 1;
}

.username {
  font-weight: 600;
  font-size: var(--font-size-md);
  color: var(--font-color-primary);
  margin-bottom: 2px;
  cursor: pointer;
}

.post-meta {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--font-color-light);
}

.post-time {
  margin-right: 8px;
}

.post-location {
  display: flex;
  align-items: center;
}

.post-location svg {
  margin-right: 2px;
}

.post-content {
  margin-bottom: 12px;
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--font-color-primary);
  word-break: break-word;
}

.post-images {
  display: grid;
  gap: 4px;
  margin-bottom: 12px;
  border-radius: var(--border-radius-sm);
  overflow: hidden;
}

.post-image-item {
  position: relative;
  padding-bottom: 100%;
  overflow: hidden;
  cursor: pointer;
}

.post-image-item img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.single-image {
  grid-template-columns: 1fr;
}

.single-image .post-image-item {
  padding-bottom: 66.67%;
}

.double-image {
  grid-template-columns: repeat(2, 1fr);
}

.triple-image {
  grid-template-columns: repeat(3, 1fr);
}

.quad-image {
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
}

.multi-image {
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: auto;
}

.post-actions {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-sm) 0;
  position: relative;
}

.action-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
  position: relative;
}

.action-item svg {
  transition: all var(--transition-fast);
}

.action-item:hover svg {
  transform: scale(1.1);
  color: var(--color-primary);
}

.liked {
  color: var(--color-primary);
}

/* 更多操作菜单样式 */
.more-menu {
  position: absolute;
  top: 100%;
  right: 0;
  width: 150px;
  background-color: #fff;
  border-radius: var(--border-radius-sm);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 15px;
  transition: background-color var(--transition-fast);
}

.menu-item:hover {
  background-color: var(--color-background);
}

/* 私密标记样式 */
.privacy-indicator {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--font-color-light);
  background-color: var(--color-background);
  padding: 3px var(--spacing-sm);
  border-radius: 10px;
}

.privacy-indicator svg {
  color: var(--color-primary);
}

.post-card-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
}

.placeholder-text {
  color: var(--font-color-light);
  font-size: var(--font-size-md);
}

/* 图片预览样式 */
.image-preview {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.image-preview img {
  max-width: 90%;
  max-height: 90%;
  border-radius: var(--border-radius);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.image-preview img.loaded {
  opacity: 1;
}

.image-preview-loading {
  width: 48px;
  height: 48px;
  position: absolute;
  border: 3px solid transparent;
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 过渡效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-normal);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 替换帖子操作区域中的SVG颜色 */
.post-actions svg {
  color: var(--font-color-light);
}
</style>
