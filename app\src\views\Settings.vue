<template>
  <page-layout title="设置" :show-back="true" hide-tab-bar hide-safe-area>
    <div class="settings-container">
      <div class="settings-section">
        <div class="section-title">账号设置</div>
        <div class="menu-list">
          <menu-item icon="icon-profile" icon-color="blue" title="个人资料" @click="handleProfileClick" />
          <menu-item icon="icon-password" icon-color="blue" title="修改密码" @click="handlePasswordClick" />
        </div>
      </div>

      <div class="settings-section">
        <div class="section-title">通知设置</div>
        <div class="menu-list">
          <menu-item icon="icon-notification" icon-color="green" title="通知管理" @click="handleNotificationClick" />
        </div>
      </div>

      <div class="settings-section">
        <div class="section-title">关于</div>
        <div class="menu-list">
          <menu-item icon="icon-terms" icon-color="purple" title="服务条款" @click="handleTermsClick" />
        </div>
      </div>

      <div class="logout-button" @click="handleLogout">退出登录</div>
    </div>
  </page-layout>
</template>

<script setup>
import { useRouter } from 'vue-router'
import PageLayout from '../components/PageLayout.vue'
import MenuItem from '../components/MenuItem.vue'
import { useUserStore } from '../stores/userStore'

const router = useRouter()
const userStore = useUserStore()

// 个人资料
const handleProfileClick = () => {
  router.push('/my-profile')
}

// 修改密码
const handlePasswordClick = () => {
  router.push('/change-password')
}

// 通知管理
const handleNotificationClick = () => {
  router.push('/notification-settings')
}

// 服务条款
const handleTermsClick = () => {
  router.push('/terms-of-service')
}

// 退出登录
const handleLogout = () => {
  userStore.logout()
  router.push('/login')
}
</script>

<style scoped>
.settings-container {
  padding-bottom: 30px;
}

.settings-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 14px;
  color: #999;
  padding: 10px 15px;
}

.menu-list {
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.logout-button {
  margin-top: 30px;
  background-color: #fff;
  color: #ff5864;
  text-align: center;
  padding: 15px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.logout-button:active {
  background-color: #f9f9f9;
}
</style>
