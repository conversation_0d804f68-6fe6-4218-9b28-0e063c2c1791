<template>
  <div class="tab-header">
    <div v-for="(tab, index) in tabs" :key="index" class="tab-header-item" :class="{ active: modelValue === index }"
      @click="$emit('update:modelValue', index)">
      {{ tab.name }}
      <span v-if="tab.count" class="tab-count">{{ tab.count }}</span>
    </div>
  </div>
</template>

<script setup>
defineProps({
  tabs: {
    type: Array,
    required: true
  },
  modelValue: {
    type: Number,
    default: 0
  }
})

defineEmits(['update:modelValue'])
</script>

<style scoped>
.tab-header {
  display: flex;
  background-color: #fff;
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-card);
  overflow: hidden;
  position: relative;
  z-index: var(--z-index-header);
}

.tab-header-item {
  flex: 1;
  text-align: center;
  padding: var(--spacing-md) 0;
  font-size: var(--font-size-md);
  color: var(--font-color-tertiary);
  position: relative;
  cursor: pointer;
}

.tab-header-item.active {
  color: var(--color-primary);
  font-weight: 500;
}

.tab-header-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background-color: var(--color-primary);
  border-radius: 2px;
}

.tab-count {
  display: inline-block;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
  padding: 0 var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: #fff;
  background-color: var(--color-primary);
  border-radius: 8px;
  text-align: center;
  margin-left: var(--spacing-xs);
}
</style>
