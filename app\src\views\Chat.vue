<template>
  <page-layout :show-back="true" hide-tab-bar hide-safe-area title="聊天">
    <template #title>
      <div class="header-title">
        <div class="avatar-container" @click="viewProfile(contactId)">
          <avatar :src="contactInfo?.avatar" size="small" />
        </div>
        <span>{{ contactInfo?.nickname || contactInfo?.username || contactInfo?.name || '聊天' }}</span>
      </div>
    </template>
    <template #header-right>
      <div class="chat-options" @click="showOptions = true">
        <i class="icon-more">⋮</i>
      </div>
    </template>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="skeleton-container">
        <div class="skeleton-date" />
        <div class="skeleton-message other" />
        <div class="skeleton-message self" />
        <div class="skeleton-message other" />
      </div>
    </div>

    <!-- 聊天消息区域 -->
    <div v-else ref="chatContainer" class="chat-container">
      <div class="chat-date">{{ formatDate(new Date()) }}</div>

      <!-- 消息列表 -->
      <div class="message-list">
        <!-- 消息项 -->
        <template v-for="(message, index) in chatMessages" :key="index">
          <!-- 对方消息 -->
          <div v-if="!message.isSelf" class="message-row other-message">
            <div class="avatar-container" @click="viewProfile(contactId)">
              <avatar :src="contactInfo?.avatar" size="small" />
            </div>
            <div class="message-content">
              <div class="message-container">
                <!-- 文本消息 -->
                <div v-if="message.type === 'text'" class="message-bubble">
                  <div class="message-text">{{ message.content }}</div>
                </div>

                <!-- 图片消息 -->
                <div v-else-if="message.type === 'image'" class="message-bubble image-bubble">
                  <div v-if="!message.loaded && message.loading" class="image-loading" />
                  <img v-lazy="getMessageImageUrl(message.content)" :class="{ loaded: message.loaded }"
                    @click="previewImage(message.content)" @load="onImageLoad(message, index)"
                    @error="onImageError(message)" />
                </div>
              </div>
              <div class="message-info">
                <span class="message-time">{{ formatMessageTime(message.time) }}</span>
              </div>
            </div>
          </div>

          <!-- 自己消息 -->
          <div v-else class="message-row self-message">
            <div class="message-content">
              <div class="message-container">
                <!-- 文本消息 -->
                <div v-if="message.type === 'text'" class="message-bubble">
                  <div class="message-text">{{ message.content }}</div>
                </div>

                <!-- 图片消息 -->
                <div v-else-if="message.type === 'image'" class="message-bubble image-bubble">
                  <div v-if="!message.loaded && message.loading" class="image-loading" />
                  <img v-lazy="getMessageImageUrl(message.content)" :class="{ loaded: message.loaded }"
                    @click="previewImage(message.content)" @load="onImageLoad(message, index)"
                    @error="onImageError(message)" />
                </div>
              </div>
              <div class="message-info">
                <span v-if="message.status === 'sending'" class="status-sending">发送中...</span>
                <span v-else-if="message.status === 'failed'" class="status-error">发送失败</span>
                <span v-else-if="message.status === 'sent'" class="status-sent">已发送</span>
                <span v-else-if="message.status === 'read'" class="status-read">已读</span>
                <span class="message-time">{{ formatMessageTime(message.time) }}</span>
              </div>
            </div>
            <div class="avatar-container">
              <avatar :src="messageStore.currentUser.avatar" size="small" />
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-container">
      <!-- 表情按钮 -->
      <div class="input-action" @click="toggleEmojiPanel">
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10" />
          <path d="M8 14s1.5 2 4 2 4-2 4-2" />
          <line x1="9" y1="9" x2="9.01" y2="9" />
          <line x1="15" y1="9" x2="15.01" y2="9" />
        </svg>
      </div>

      <!-- 文本输入框 -->
      <div class="input-box">
        <textarea ref="messageInput" v-model="inputMessage" placeholder="输入消息..." @keydown.enter.prevent="sendMessage"
          @input="adjustTextareaHeight" />
      </div>

      <!-- 图片按钮 -->
      <div class="input-action" @click="triggerImageUpload">
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
          <circle cx="8.5" cy="8.5" r="1.5" />
          <polyline points="21 15 16 10 5 21" />
        </svg>
        <input ref="imageInput" type="file" accept="image/*" style="display: none" @change="handleImageUpload" />
      </div>

      <!-- 发送按钮 -->
      <div class="input-action send-button" :class="{ active: inputMessage.trim().length > 0 }" @click="sendMessage">
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="22" y1="2" x2="11" y2="13" />
          <polygon points="22 2 15 22 11 13 2 9 22 2" />
        </svg>
      </div>

      <!-- 表情面板组件 -->
      <emoji-panel v-model="showEmojiPanel" @select="insertEmoji" />
    </div>

    <!-- 图片预览 -->
    <transition name="fade">
      <div v-if="previewImageUrl" class="image-preview" @click="closePreview">
        <div v-if="previewLoading" class="image-preview-loading" />
        <img v-lazy="previewImageUrl" :class="{ loaded: !previewLoading }" @load="previewLoading = false" />
      </div>
    </transition>

    <!-- 聊天选项菜单 -->
    <transition name="fade">
      <div v-if="showOptions" class="options-panel" @click="showOptions = false">
        <transition name="slide-up">
          <div v-if="showOptions" class="options-content" @click.stop>
            <div class="options-header">
              <span>聊天选项</span>
              <span class="options-close" @click="showOptions = false">×</span>
            </div>

            <div class="options-group">
              <div class="option-item" @click="viewProfile(contactId)">
                <div class="option-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
                    <circle cx="12" cy="7" r="4" />
                  </svg>
                </div>
                <span>查看资料</span>
              </div>
              <div class="option-item" @click="clearChat">
                <div class="option-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="3 6 5 6 21 6" />
                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
                  </svg>
                </div>
                <span>清空聊天记录</span>
              </div>
              <div class="option-item" @click="unmatchContact">
                <div class="option-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M18 6L6 18" />
                    <path d="M6 6l12 12" />
                  </svg>
                </div>
                <span>解除匹配</span>
              </div>
              <div class="option-item" @click="reportContact">
                <div class="option-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path
                      d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" />
                    <line x1="12" y1="9" x2="12" y2="13" />
                    <line x1="12" y1="17" x2="12.01" y2="17" />
                  </svg>
                </div>
                <span>举报</span>
              </div>
              <div class="option-item" @click="blockContact">
                <div class="option-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10" />
                    <line x1="4.93" y1="4.93" x2="19.07" y2="19.07" />
                  </svg>
                </div>
                <span>屏蔽此人</span>
              </div>
            </div>

            <div class="option-item cancel" @click="showOptions = false">取消</div>
          </div>
        </transition>
      </div>
    </transition>
  </page-layout>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import PageLayout from '../components/PageLayout.vue'
import Avatar from '../components/Avatar.vue'
import EmojiPanel from '../components/EmojiPanel.vue'
import { useMessageStore } from '../stores/messageStore'
import { getImageUrl } from '../config'

const route = useRoute()
const router = useRouter()
const chatContainer = ref(null)
const messageInput = ref(null)
const imageInput = ref(null)
const messageStore = useMessageStore()

// 加载状态
const isLoading = ref(true)

// 获取联系人ID
const contactId = computed(() => Number(route.params.id))

// 获取联系人信息
const contactInfo = computed(() => {
  return messageStore.getContactById(contactId.value)
})

// 获取聊天消息
const chatMessages = computed(() => {
  return messageStore.getMessagesByContactId(contactId.value)
})

// 输入框内容
const inputMessage = ref('')

// 表情面板
const showEmojiPanel = ref(false)

// 图片预览
const previewImageUrl = ref(null)
const previewLoading = ref(true)

// 聊天选项
const showOptions = ref(false)

// 页面可见性处理
const handleVisibilityChange = () => {
  if (document.visibilityState === 'visible') {
    // 页面重新可见，标记已读
    if (contactId.value) {
      messageStore.markAsRead(contactId.value)
    }
  } else {
    // 页面不可见，最后标记已读
    if (contactId.value) {
      messageStore.markAsRead(contactId.value)
    }
  }
}

const handleBeforeUnload = () => {
  // 页面即将隐藏，标记已读
  if (contactId.value) {
    messageStore.markAsRead(contactId.value)
  }
}

// 滚动到底部
const scrollToBottom = (smooth = false) => {
  nextTick(() => {
    if (chatContainer.value) {
      if (smooth) {
        chatContainer.value.scrollTo({
          top: chatContainer.value.scrollHeight,
          behavior: 'smooth'
        })
      } else {
        chatContainer.value.scrollTop = chatContainer.value.scrollHeight
      }
    }
  })
}

// 监听消息变化，自动滚动到底部并标记已读
watch(
  chatMessages,
  (newMessages, oldMessages) => {
    // 如果是接收到新的对方消息，确保已读状态
    if (newMessages.length > oldMessages.length) {
      const lastMessage = newMessages[newMessages.length - 1]
      if (!lastMessage.isSelf && contactId.value) {
        // 收到新的对方消息，确保已读状态
        nextTick(() => {
          messageStore.markAsRead(contactId.value)
          scrollToBottom()
        })
      }
    }

    // 自动滚动到底部
    nextTick(() => {
      scrollToBottom()
    })
  },
  { flush: 'post' }
)

// 格式化日期
const formatDate = date => {
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  if (date.toDateString() === today.toDateString()) {
    return '今天'
  } else if (date.toDateString() === yesterday.toDateString()) {
    return '昨天'
  }

  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
}

// 格式化消息时间
const formatMessageTime = time => {
  const date = new Date(time)
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
}

// 发送消息
const sendMessage = () => {
  const text = inputMessage.value.trim()
  if (text.length === 0) {
    return
  }

  // 添加新消息
  messageStore.sendMessage(contactId.value, text)

  // 清空输入框
  inputMessage.value = ''

  // 重置输入框高度
  if (messageInput.value) {
    messageInput.value.style.height = 'auto'
    messageInput.value.style.height = '24px'
  }

  // 关闭表情面板
  if (showEmojiPanel.value) {
    showEmojiPanel.value = false
  }

  // 立即滚动到底部
  scrollToBottom(true)
}

// 调整输入框高度
const adjustTextareaHeight = () => {
  if (!messageInput.value) {
    return
  }

  messageInput.value.style.height = 'auto'
  messageInput.value.style.height = Math.min(messageInput.value.scrollHeight, 100) + 'px'
}

// 切换表情面板
const toggleEmojiPanel = () => {
  showEmojiPanel.value = !showEmojiPanel.value
}

// 插入表情
const insertEmoji = emoji => {
  inputMessage.value += emoji
  adjustTextareaHeight()
}

// 触发图片上传
const triggerImageUpload = () => {
  imageInput.value.click()
}

// 处理图片上传（通过messageStore）
const handleImageUpload = async (event) => {
  const file = event.target.files[0]
  if (!file) {
    return
  }

  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    alert('请选择图片文件')
    return
  }

  // 检查文件大小
  if (file.size > 10 * 1024 * 1024) {
    alert('图片大小不能超过10MB')
    return
  }

  try {
    // 通过messageStore发送图片
    await messageStore.sendImageMessage(contactId.value, file)

    // 滚动到底部
    scrollToBottom(true)
  } catch (error) {
    // 显示错误提示
    toast.error('发送图片失败，请重试')
  }

  // 重置文件输入
  event.target.value = ''
}

// 预览图片
const previewImage = url => {
  previewImageUrl.value = getImageUrl(url)
  previewLoading.value = true
}

// 获取图片URL的辅助函数
const getMessageImageUrl = (content) => {
  return getImageUrl(content)
}

// 关闭预览
const closePreview = () => {
  previewImageUrl.value = null
}

// 图片加载完成事件
const onImageLoad = (message, index) => {
  messageStore.setImageLoaded(contactId.value, index)
}

// 图片加载错误事件
const onImageError = message => {
  message.loading = false
  message.error = true
}

// 清空聊天记录
const clearChat = () => {
  if (confirm('确定要清空聊天记录吗？')) {
    messageStore.clearChat(contactId.value)
    showOptions.value = false
  }
}

// 解除匹配
const unmatchContact = () => {
  if (confirm('确定要解除匹配吗？')) {
    alert('已解除匹配')
    showOptions.value = false
  }
}

// 举报
const reportContact = () => {
  if (confirm('确定要举报此人吗？')) {
    alert('已提交举报')
    showOptions.value = false
  }
}

// 屏蔽联系人
const blockContact = () => {
  if (confirm('确定要屏蔽此人吗？')) {
    alert('已屏蔽该用户')
    showOptions.value = false
  }
}

// 监听输入框内容变化
watch(inputMessage, () => {
  adjustTextareaHeight()
})

// 监听联系人ID变化
watch(contactId, async (newContactId, oldContactId) => {
  if (newContactId && newContactId !== oldContactId) {
    // 设置当前聊天联系人ID
    messageStore.setCurrentChatContact(newContactId)

    // 联系人切换逻辑
    await messageStore.loadChatHistory(newContactId)

    // 标记为已读
    messageStore.markAsRead(newContactId)

    await nextTick()
    scrollToBottom()
  }
})

// 组件挂载后执行
onMounted(async () => {
  try {
    // 设置当前聊天联系人ID
    messageStore.setCurrentChatContact(contactId.value)

    // 加载聊天记录
    await messageStore.loadChatHistory(contactId.value)

    // 将联系人的未读消息数量置为0（标记为已读）
    messageStore.markAsRead(contactId.value)

    setTimeout(() => {
      isLoading.value = false
      scrollToBottom()
    }, 300)

    // 添加事件监听器
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('beforeunload', handleBeforeUnload)
  } catch (error) {
    // 静默处理初始化聊天页面失败
  }
})

// 在组件卸载前移除事件监听
onBeforeUnmount(() => {
  // Chat页面即将卸载，最后标记已读
  if (contactId.value) {
    messageStore.markAsRead(contactId.value)
  }

  // 移除事件监听
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  window.removeEventListener('beforeunload', handleBeforeUnload)

  // 清除当前聊天联系人状态
  messageStore.clearCurrentChatContact()
})

// 点击用户头像跳转到用户资料页面的功能
const viewProfile = userId => {
  router.push(`/profile/${userId}`)
}
</script>

<style scoped>
.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-title .avatar-container {
  cursor: pointer;
}

.chat-options {
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 0 5px;
}

/* 加载状态和骨架屏 */
.loading-container {
  flex: 1;
  overflow-y: auto;
  margin: 0 -15px;
  padding: 0 15px 10px;
  background-color: #ffffff;
}

.skeleton-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px 0;
}

.skeleton-date {
  height: 20px;
  width: 80px;
  background-color: #f0f0f0;
  border-radius: 10px;
  margin: 10px auto;
  animation: pulse 1.5s infinite;
}

.skeleton-message {
  height: 40px;
  border-radius: 18px;
  background-color: #f0f0f0;
  animation: pulse 1.5s infinite;
}

.skeleton-message.other {
  width: 70%;
  align-self: flex-start;
  margin-left: 48px;
  border-top-left-radius: 4px;
}

.skeleton-message.self {
  width: 60%;
  align-self: flex-end;
  margin-right: 48px;
  border-top-right-radius: 4px;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.6;
  }
}

.chat-container {
  flex: 1;
  overflow-y: auto;
  margin: 0 -15px;
  padding: 0 15px 10px;
  background-color: #ffffff;
}

.chat-date {
  text-align: center;
  font-size: 12px;
  color: #999;
  margin: 10px 0;
  padding: 4px 12px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  display: inline-block;
  margin-left: auto;
  margin-right: auto;
  width: auto;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 消息行 */
.message-row {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 对方消息 */
.other-message {
  align-self: flex-start;
  margin-right: 60px;
}

/* 自己消息 */
.self-message {
  align-self: flex-end;
  margin-left: 60px;
  justify-content: flex-end;
}

/* 头像容器 */
.avatar-container {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

/* 消息内容区 */
.message-content {
  display: flex;
  flex-direction: column;
  max-width: 100%;
}

.self-message .message-content {
  align-items: flex-end;
}

/* 消息容器 */
.message-container {
  display: flex;
  flex-direction: column;
  max-width: 100%;
}

/* 消息气泡 */
.message-bubble {
  border-radius: 18px;
  padding: 10px 15px;
  position: relative;
  word-break: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  max-width: 100%;
  contain: content;
  transform: translate3d(0, 0, 0);
  will-change: transform;
  transition:
    transform 0.2s ease,
    opacity 0.2s ease;
}

.other-message .message-bubble {
  background-color: white;
  color: #333;
  border-top-left-radius: 4px;
}

.self-message .message-bubble {
  background-color: #ff6b6b;
  color: white;
  border-top-right-radius: 4px;
}

.message-text {
  font-size: 15px;
  line-height: 1.4;
}

.image-bubble {
  padding: 3px;
  background-color: transparent;
  overflow: hidden;
  box-shadow: none;
  position: relative;
}

.image-bubble img {
  max-width: 100%;
  max-height: 200px;
  border-radius: 12px;
  display: block;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-bubble img.loaded {
  opacity: 1;
}

.image-loading {
  width: 80px;
  height: 80px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-loading:after {
  content: '';
  width: 24px;
  height: 24px;
  border: 2px solid transparent;
  border-top-color: #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.message-info {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.self-message .message-info {
  flex-direction: row-reverse;
}

.message-time {
  color: #999;
}

.status-sending {
  color: #ff9800;
}

.status-error {
  color: #f44336;
}

.status-sent {
  color: #2196f3;
}

.status-read {
  color: #8bc34a;
}

.input-container {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-top: 1px solid #f0f0f0;
  background-color: #fff;
  position: relative;
  z-index: 20;
  min-height: 60px;
  box-sizing: border-box;
  margin-bottom: 0;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  will-change: transform;
  padding-top: 15px;
}

.input-action {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #666;
  border-radius: 50%;
  transition: background-color 0.2s;
  flex-shrink: 0;
}

.input-action:hover {
  background-color: #f5f5f5;
}

.input-action svg {
  width: 22px;
  height: 22px;
}

.input-box {
  flex: 1;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 10px 15px;
  margin: 0 10px;
  transition: box-shadow 0.2s;
  min-height: 44px;
  display: flex;
  align-items: center;
}

.input-box:focus-within {
  box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.2);
}

.input-box textarea {
  width: 100%;
  border: none;
  background-color: transparent;
  resize: none;
  outline: none;
  font-size: 15px;
  line-height: 1.4;
  max-height: 120px;
  min-height: 24px;
  padding: 0;
  font-family: inherit;
}

.send-button {
  color: #ccc;
}

.send-button.active {
  color: #ff6b6b;
}

.send-button svg {
  fill: currentColor;
  stroke: none;
}

.image-preview {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.image-preview img {
  max-width: 90%;
  max-height: 90%;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-preview img.loaded {
  opacity: 1;
}

.image-preview-loading {
  width: 48px;
  height: 48px;
  position: absolute;
  border: 3px solid transparent;
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.options-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1000;
}

.options-content {
  width: 100%;
  background-color: #fff;
  border-radius: 20px 20px 0 0;
  overflow: hidden;
  max-width: 600px;
  transform: translateY(0);
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}

.options-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.options-header span {
  font-size: 16px;
  font-weight: 500;
}

.options-close {
  font-size: 22px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
}

.options-close:hover {
  background-color: #f5f5f5;
}

.options-group {
  padding: 5px 0;
  border-bottom: 1px solid #f5f5f5;
}

.group-title {
  font-size: 13px;
  color: #999;
  padding: 8px 15px;
}

.option-item {
  padding: 15px;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
  cursor: pointer;
}

.option-item:active {
  background-color: #f5f5f5;
}

.option-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: #666;
}

.option-item:hover .option-icon {
  background-color: #ebebeb;
}

.option-item span {
  font-size: 16px;
}

.option-item.cancel {
  text-align: center;
  color: #ff6b6b;
  font-weight: 500;
  justify-content: center;
  padding: 18px;
  margin: 8px 0;
}

/* 过渡效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.panel-enter-active,
.panel-leave-active {
  transition:
    transform 0.3s ease,
    opacity 0.3s ease;
}

.panel-enter-from,
.panel-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(100%);
}

/* 消息动画 */
.message-row {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 提升渲染性能相关CSS */
.chat-container,
.loading-container {
  -webkit-overflow-scrolling: touch;
  /* iOS平滑滚动 */
  will-change: transform;
  /* 提示浏览器将创建单独图层 */
  transform: translate3d(0, 0, 0);
  /* 促进硬件加速 */
  backface-visibility: hidden;
  /* 减少闪烁 */
}

/* 输入容器和面板优化 */
.input-container,
.emoji-panel,
.options-panel {
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  will-change: transform;
}
</style>
