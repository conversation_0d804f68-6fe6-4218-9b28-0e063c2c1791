<template>
  <page-layout title="聊天">
    <!-- 匹配部分 -->
    <div class="match-container">
      <h2 class="section-title">新的匹配</h2>

      <!-- 空状态 -->
      <empty-state v-if="messageStore.matches.length === 0" icon="搜索" text="暂无新匹配" />

      <!-- 匹配列表 -->
      <div v-else class="match-list">
        <user-card
          v-for="(match, index) in messageStore.matches"
          :key="`match-${index}`"
          type="match"
          :username="match.nickname || match.username"
          :avatar="match.avatar"
          :is-new="match.isNew"
          @click="viewProfile(match.userId || match.id)"
        />
      </div>
    </div>

    <!-- 联系人部分 -->
    <div class="contact-container">
      <h2 class="section-title">联系人</h2>

      <!-- 空状态 -->
      <empty-state v-if="messageStore.contacts.length === 0" icon="消息" text="暂无联系人" />

      <!-- 联系人列表 -->
      <div v-else class="contact-list">
        <user-card
          v-for="(contact, index) in messageStore.contacts"
          :key="`contact-${index}`"
          type="contact"
          :username="contact.nickname || contact.username"
          :avatar="contact.avatar"
          :last-message="contact.lastMessage?.content"
          :last-message-time="formatTime(contact.lastMessage?.time || contact.lastMessageTime)"
          :unread-count="contact.unreadCount"
          @click="goToChat(contact.id)"
        />
      </div>
    </div>
  </page-layout>
</template>

<script setup>
  import { onMounted, onActivated, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import PageLayout from '../components/PageLayout.vue'
  import EmptyState from '../components/EmptyState.vue'
  import UserCard from '../components/UserCard.vue'
  import { useMessageStore } from '../stores/messageStore'

  const router = useRouter()
  const messageStore = useMessageStore()

  // 初始化数据的公共方法
  const initializeMessageData = async () => {
    // 清除当前聊天联系人状态（确保不会误判为在聊天页面）
    messageStore.clearCurrentChatContact()
    
    // 强制刷新联系人数据，确保显示最新状态
    await messageStore.forceRefreshContacts()
  }

  // 页面挂载时初始化数据
  onMounted(initializeMessageData)
  
  // 页面重新激活时刷新数据（解决从其他页面返回时的数据同步问题）
  onActivated(async () => {
    await initializeMessageData()
  })

  // 监听联系人数据变化（移除console输出）
  watch(() => messageStore.contacts, (newContacts) => {
    // 联系人数据已更新
  }, { deep: true })

  // 格式化时间
  const formatTime = timestamp => {
    if (!timestamp) return ''
    
    const now = Date.now()
    const time = typeof timestamp === 'string' ? new Date(timestamp).getTime() : timestamp
    const diff = now - time

    // 小于1分钟
    if (diff < 60000) {
      return '刚刚'
    }

    // 小于1小时
    if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`
    }

    // 小于24小时
    if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}小时前`
    }

    // 小于30天
    if (diff < 2592000000) {
      return `${Math.floor(diff / 86400000)}天前`
    }

    // 大于30天，显示具体日期
    const date = new Date(time)
    return `${date.getMonth() + 1}月${date.getDate()}日`
  }

  // 跳转到聊天页面
  const goToChat = id => {
    router.push(`/chat/${id}`)
  }

  // 查看用户资料
  const viewProfile = id => {
    router.push(`/profile/${id}`)
  }
</script>

<style scoped>
  /* Message页面特定样式 */
  .section-title {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    margin: 10px 0 8px;
  }

  /* 匹配部分 */
  .match-container {
    margin-bottom: 15px;
    width: 100%;
  }

  .match-list {
    display: flex;
    overflow-x: auto;
    padding: 5px 0;
    -ms-overflow-style: none;
    scrollbar-width: none;
    width: 100%;
    gap: 12px;
  }

  .match-list::-webkit-scrollbar {
    display: none;
  }

  /* 联系人部分 */
  .contact-container {
    flex: 1;
    width: 100%;
  }

  .contact-list {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    width: 100%;
  }
</style>
