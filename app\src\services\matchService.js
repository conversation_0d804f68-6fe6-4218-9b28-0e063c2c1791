import { http } from '../utils/request.js'

/**
 * 匹配系统相关 API 服务
 */
export const matchService = {
  /**
   * 获取推荐用户列表
   * @param {Object} filters - 筛选条件
   * @param {string} filters.gender - 性别筛选
   * @param {number} filters.age_min - 最小年龄
   * @param {number} filters.age_max - 最大年龄
   * @param {string} filters.provinceCode - 省份代码
   * @param {string[]} filters.tags - 标签数组
   * @returns {Promise}
   */
  async getRecommendations(filters = {}) {
    try {
      const response = await http.get('/matches/recommendations', filters)
      return response
    } catch (error) {
      throw new Error(error.message || '获取推荐用户失败')
    }
  },

  /**
   * 匹配操作（喜欢/不喜欢）
   * @param {number} targetUserId - 目标用户ID
   * @param {string} action - 操作类型：'like' 或 'dislike'
   * @returns {Promise}
   */
  async performAction(targetUserId, action) {
    try {
      const response = await http.post('/matches/action', {
        targetUserId,
        action
      })
      return response
    } catch (error) {
      throw new Error(error.message || '操作失败')
    }
  },

  /**
   * 获取匹配列表
   * @returns {Promise}
   */
  async getMatches() {
    try {
      const response = await http.get('/matches')
      return response
    } catch (error) {
      throw new Error(error.message || '获取匹配列表失败')
    }
  },

  /**
   * 获取喜欢我的用户列表
   * @returns {Promise}
   */
  async getReceivedLikes() {
    try {
      const response = await http.get('/likes/received')
      return response
    } catch (error) {
      throw new Error(error.message || '获取喜欢我的用户列表失败')
    }
  },

  /**
   * 获取我喜欢的用户列表
   * @returns {Promise}
   */
  async getSentLikes() {
    try {
      const response = await http.get('/likes/sent')
      return response
    } catch (error) {
      throw new Error(error.message || '获取我喜欢的用户列表失败')
    }
  }
} 